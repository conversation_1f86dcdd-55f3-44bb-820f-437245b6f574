package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
	"gopkg.in/yaml.v3"
)

// Config represents the application configuration
type Config struct {
	Environments EnvironmentsConfig `yaml:"environments" mapstructure:"environments"`
	Security     SecurityConfig     `yaml:"security" mapstructure:"security"`
	Dagger       DaggerConfig       `yaml:"dagger" mapstructure:"dagger"`
	Porter       PorterConfig       `yaml:"porter" mapstructure:"porter"`
	Compliance   ComplianceConfig   `yaml:"compliance" mapstructure:"compliance"`
	Logging      LoggingConfig      `yaml:"logging" mapstructure:"logging"`
	TUI          TUIConfig          `yaml:"tui" mapstructure:"tui"`
}

// EnvironmentsConfig defines environment-specific configurations
type EnvironmentsConfig struct {
	Online       OnlineEnvironment       `yaml:"online" mapstructure:"online"`
	Transmission TransmissionEnvironment `yaml:"transmission" mapstructure:"transmission"`
	Airgapped    AirgappedEnvironment    `yaml:"airgapped" mapstructure:"airgapped"`
}

// OnlineEnvironment configuration for the online environment
type OnlineEnvironment struct {
	GitLab GitLabConfig `yaml:"gitlab" mapstructure:"gitlab"`
	JFrog  JFrogConfig  `yaml:"jfrog" mapstructure:"jfrog"`
}

// TransmissionEnvironment configuration for the transmission station
type TransmissionEnvironment struct {
	Endpoint string     `yaml:"endpoint" mapstructure:"endpoint"`
	Auth     AuthConfig `yaml:"auth" mapstructure:"auth"`
	Timeout  int        `yaml:"timeout" mapstructure:"timeout"`
	Retries  int        `yaml:"retries" mapstructure:"retries"`
}

// AirgappedEnvironment configuration for the airgapped environment
type AirgappedEnvironment struct {
	JFrog JFrogConfig `yaml:"jfrog" mapstructure:"jfrog"`
}

// GitLabConfig configuration for GitLab integration
type GitLabConfig struct {
	URL      string            `yaml:"url" mapstructure:"url"`
	Token    string            `yaml:"token" mapstructure:"token"`
	Projects map[string]string `yaml:"projects" mapstructure:"projects"`
}

// JFrogConfig configuration for JFrog Artifactory
type JFrogConfig struct {
	URL        string `yaml:"url" mapstructure:"url"`
	Username   string `yaml:"username" mapstructure:"username"`
	Password   string `yaml:"password" mapstructure:"password"`
	Repository string `yaml:"repository" mapstructure:"repository"`
}

// AuthConfig authentication configuration
type AuthConfig struct {
	Method   string `yaml:"method" mapstructure:"method"`
	CertPath string `yaml:"cert_path" mapstructure:"cert_path"`
	KeyPath  string `yaml:"key_path" mapstructure:"key_path"`
	CAPath   string `yaml:"ca_path" mapstructure:"ca_path"`
}

// SecurityConfig security and signing configuration
type SecurityConfig struct {
	Signing      SigningConfig      `yaml:"signing" mapstructure:"signing"`
	Verification VerificationConfig `yaml:"verification" mapstructure:"verification"`
	Encryption   EncryptionConfig   `yaml:"encryption" mapstructure:"encryption"`
	KeyStore     KeyStoreConfig     `yaml:"keystore" mapstructure:"keystore"`
}

// SigningConfig artifact signing configuration
type SigningConfig struct {
	Enabled    bool   `yaml:"enabled" mapstructure:"enabled"`
	Method     string `yaml:"method" mapstructure:"method"`
	KeyPath    string `yaml:"key_path" mapstructure:"key_path"`
	CertPath   string `yaml:"cert_path" mapstructure:"cert_path"`
	Passphrase string `yaml:"passphrase" mapstructure:"passphrase"`
}

// VerificationConfig artifact verification configuration
type VerificationConfig struct {
	Required bool     `yaml:"required" mapstructure:"required"`
	Policies []string `yaml:"policies" mapstructure:"policies"`
	TrustRoot string   `yaml:"trust_root" mapstructure:"trust_root"`
}

// EncryptionConfig encryption configuration
type EncryptionConfig struct {
	Enabled   bool   `yaml:"enabled" mapstructure:"enabled"`
	Algorithm string `yaml:"algorithm" mapstructure:"algorithm"`
	KeyPath   string `yaml:"key_path" mapstructure:"key_path"`
}

// KeyStoreConfig key store configuration
type KeyStoreConfig struct {
	Enabled    bool   `yaml:"enabled" mapstructure:"enabled"`
	Path       string `yaml:"path" mapstructure:"path"`
	Backend    string `yaml:"backend" mapstructure:"backend"`
	MasterKey  string `yaml:"master_key" mapstructure:"master_key"`
	HSMEnabled bool   `yaml:"hsm_enabled" mapstructure:"hsm_enabled"`
}

// DaggerConfig Dagger CI/CD configuration
type DaggerConfig struct {
	EngineVersion string            `yaml:"engine_version" mapstructure:"engine_version"`
	CachePolicy   string            `yaml:"cache_policy" mapstructure:"cache_policy"`
	Registry      string            `yaml:"registry" mapstructure:"registry"`
	Workdir       string            `yaml:"workdir" mapstructure:"workdir"`
	Modules       map[string]string `yaml:"modules" mapstructure:"modules"`
}

// PorterConfig Porter CNAB configuration
type PorterConfig struct {
	Registry      string            `yaml:"registry" mapstructure:"registry"`
	DefaultDriver string            `yaml:"default_driver" mapstructure:"default_driver"`
	Mixins        []string          `yaml:"mixins" mapstructure:"mixins"`
	Bundles       map[string]string `yaml:"bundles" mapstructure:"bundles"`
}

// ComplianceConfig compliance and audit configuration
type ComplianceConfig struct {
	AuditRetention      string   `yaml:"audit_retention" mapstructure:"audit_retention"`
	SBOMFormat          []string `yaml:"sbom_format" mapstructure:"sbom_format"`
	AttestationRequired bool     `yaml:"attestation_required" mapstructure:"attestation_required"`
	ReportPath          string   `yaml:"report_path" mapstructure:"report_path"`
}

// LoggingConfig logging configuration
type LoggingConfig struct {
	Level        string `yaml:"level" mapstructure:"level"`
	Format       string `yaml:"format" mapstructure:"format"`
	EnableColors bool   `yaml:"enable_colors" mapstructure:"enable_colors"`
	Output       string `yaml:"output" mapstructure:"output"`
}

// TUIConfig TUI-specific configuration
type TUIConfig struct {
	Theme       string `yaml:"theme" mapstructure:"theme"`
	RefreshRate int    `yaml:"refresh_rate" mapstructure:"refresh_rate"`
	MouseMode   bool   `yaml:"mouse_mode" mapstructure:"mouse_mode"`
}

// Load loads the configuration from file and environment variables
func Load() (*Config, error) {
	config := &Config{
		// Set default values
		Logging: LoggingConfig{
			Level:        "info",
			Format:       "text",
			EnableColors: true,
			Output:       "stdout",
		},
		TUI: TUIConfig{
			Theme:       "default",
			RefreshRate: 60,
			MouseMode:   true,
		},
		Dagger: DaggerConfig{
			EngineVersion: "0.13.0",
			CachePolicy:   "aggressive",
		},
		Porter: PorterConfig{
			DefaultDriver: "docker",
			Mixins:        []string{"exec", "docker", "kubernetes"},
		},
		Security: SecurityConfig{
			Signing: SigningConfig{
				Enabled: true,
				Method:  "sigstore",
			},
			Verification: VerificationConfig{
				Required: true,
				Policies: []string{"slsa4", "sbom"},
			},
		},
		Compliance: ComplianceConfig{
			AuditRetention:      "7y",
			SBOMFormat:          []string{"spdx", "cyclonedx"},
			AttestationRequired: true,
		},
	}

	// Setup viper
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")

	// Add config paths
	homeDir, err := os.UserHomeDir()
	if err == nil {
		viper.AddConfigPath(filepath.Join(homeDir, ".mc-poly"))
	}
	viper.AddConfigPath(".")
	viper.AddConfigPath("/etc/mc-poly")

	// Environment variable prefix
	viper.SetEnvPrefix("MCPOLY")
	viper.AutomaticEnv()

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("error reading config file: %w", err)
		}
		// Config file not found is acceptable for first run
	}

	// Unmarshal config
	if err := viper.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	return config, nil
}

// Save saves the configuration to file
func (c *Config) Save(path string) error {
	if path == "" {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return fmt.Errorf("error getting home directory: %w", err)
		}
		configDir := filepath.Join(homeDir, ".mc-poly")
		if err := os.MkdirAll(configDir, 0755); err != nil {
			return fmt.Errorf("error creating config directory: %w", err)
		}
		path = filepath.Join(configDir, "config.yaml")
	}

	data, err := yaml.Marshal(c)
	if err != nil {
		return fmt.Errorf("error marshaling config: %w", err)
	}

	if err := os.WriteFile(path, data, 0644); err != nil {
		return fmt.Errorf("error writing config file: %w", err)
	}

	return nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Basic validation logic
	if c.Environments.Online.GitLab.URL == "" {
		return fmt.Errorf("online GitLab URL is required")
	}

	if c.Environments.Transmission.Endpoint == "" {
		return fmt.Errorf("transmission station endpoint is required")
	}

	if c.Security.Signing.Enabled && c.Security.Signing.Method == "" {
		return fmt.Errorf("signing method is required when signing is enabled")
	}

	return nil
}
