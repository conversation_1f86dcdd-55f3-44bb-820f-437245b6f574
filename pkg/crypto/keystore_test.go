// Package: crypto_test
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:keystore-test:2025-01-27T10:00:00.123Z
// 
// Purpose: BDD tests for crypto keystore with secure key management
// Governance: MCStack v13.5 test compliance with comprehensive coverage
// Security: Test secure key operations without compromising real keys
package crypto_test

import (
	"os"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/suite"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/mchorfa/mc-poly-installer/pkg/crypto"
)

var _ = Describe("Crypto KeyStore", func() {
	var (
		cfg      *config.Config
		keystore *crypto.KeyStore
		tempDir  string
	)

	BeforeEach(func() {
		var err error
		tempDir, err = os.MkdirTemp("", "keystore-test-*")
		Expect(err).NotTo(HaveOccurred())

		cfg = &config.Config{
			Security: config.SecurityConfig{
				Encryption: config.EncryptionConfig{
					Enabled: true,
				},
				KeyStore: config.KeyStoreConfig{
					Enabled: true,
					Path:    tempDir,
					Backend: "file",
				},
			},
		}
	})

	AfterEach(func() {
		if tempDir != "" {
			os.RemoveAll(tempDir)
		}
	})

	Describe("Creating a new keystore", func() {
		Context("when keystore is enabled", func() {
			It("should create a keystore successfully", func() {
				var err error
				keystore, err = crypto.NewKeyStore(cfg)
				Expect(err).NotTo(HaveOccurred())
				Expect(keystore).NotTo(BeNil())
			})

			It("should create the keystore directory", func() {
				_, err := crypto.NewKeyStore(cfg)
				Expect(err).NotTo(HaveOccurred())
				Expect(tempDir).To(BeADirectory())
			})
		})

		Context("when keystore is disabled", func() {
			BeforeEach(func() {
				cfg.Security.KeyStore.Enabled = false
			})

			It("should return an error", func() {
				_, err := crypto.NewKeyStore(cfg)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("keystore is disabled"))
			})
		})
	})

	Describe("Key generation", func() {
		BeforeEach(func() {
			var err error
			keystore, err = crypto.NewKeyStore(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when generating a symmetric key", func() {
			It("should generate and store a symmetric key", func() {
				opts := &crypto.KeyGenerationOptions{
					Name:      "test-symmetric-key",
					Algorithm: "AES-256",
					KeyType:   crypto.SymmetricKey,
					Usage:     []crypto.KeyUsage{crypto.UsageEncrypt, crypto.UsageDecrypt},
				}

				storageOpts := &crypto.KeyStorageOptions{
					Password: "test-password-123",
				}

				entry, err := keystore.GenerateKey(opts, storageOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(entry).NotTo(BeNil())
				Expect(entry.ID).NotTo(BeEmpty())
				Expect(entry.Name).To(Equal("test-symmetric-key"))
				Expect(entry.Algorithm).To(Equal("AES-256"))
				Expect(entry.KeyType).To(Equal(string(crypto.SymmetricKey)))
				Expect(entry.Usage).To(ContainElements("encrypt", "decrypt"))
				Expect(entry.Created).To(BeTemporally("~", time.Now(), time.Minute))
				Expect(entry.Revoked).To(BeFalse())
			})
		})

		Context("when generating a master key", func() {
			It("should generate and store a master key", func() {
				opts := &crypto.KeyGenerationOptions{
					Name:      "test-master-key",
					Algorithm: "AES-256",
					KeyType:   crypto.MasterKey,
					Usage:     []crypto.KeyUsage{crypto.UsageKeyWrap, crypto.UsageKeyUnwrap},
				}

				storageOpts := &crypto.KeyStorageOptions{
					Password: "master-password-456",
				}

				entry, err := keystore.GenerateKey(opts, storageOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(entry.KeyType).To(Equal(string(crypto.MasterKey)))
				Expect(entry.Usage).To(ContainElements("key_wrap", "key_unwrap"))
			})
		})

		Context("when generating with expiration", func() {
			It("should set expiration time", func() {
				expiresAt := time.Now().Add(24 * time.Hour)
				opts := &crypto.KeyGenerationOptions{
					Name:      "expiring-key",
					Algorithm: "AES-256",
					KeyType:   crypto.SymmetricKey,
					Usage:     []crypto.KeyUsage{crypto.UsageEncrypt},
					ExpiresAt: &expiresAt,
				}

				storageOpts := &crypto.KeyStorageOptions{
					Password: "test-password",
				}

				entry, err := keystore.GenerateKey(opts, storageOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(entry.ExpiresAt).NotTo(BeNil())
				Expect(*entry.ExpiresAt).To(BeTemporally("~", expiresAt, time.Second))
			})
		})

		Context("when generating with metadata", func() {
			It("should store metadata", func() {
				metadata := map[string]string{
					"environment": "test",
					"owner":       "security-team",
				}

				opts := &crypto.KeyGenerationOptions{
					Name:      "metadata-key",
					Algorithm: "AES-256",
					KeyType:   crypto.SymmetricKey,
					Usage:     []crypto.KeyUsage{crypto.UsageEncrypt},
					Metadata:  metadata,
				}

				storageOpts := &crypto.KeyStorageOptions{
					Password: "test-password",
				}

				entry, err := keystore.GenerateKey(opts, storageOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(entry.Metadata).To(Equal(metadata))
			})
		})

		Context("when no storage options provided", func() {
			It("should return an error", func() {
				opts := &crypto.KeyGenerationOptions{
					Name:      "test-key",
					Algorithm: "AES-256",
					KeyType:   crypto.SymmetricKey,
					Usage:     []crypto.KeyUsage{crypto.UsageEncrypt},
				}

				_, err := keystore.GenerateKey(opts, &crypto.KeyStorageOptions{})
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("either password or master key must be provided"))
			})
		})
	})

	Describe("Key retrieval", func() {
		var keyEntry *crypto.KeyEntry
		var keyPassword string

		BeforeEach(func() {
			var err error
			keystore, err = crypto.NewKeyStore(cfg)
			Expect(err).NotTo(HaveOccurred())

			keyPassword = "retrieval-test-password"
			opts := &crypto.KeyGenerationOptions{
				Name:      "retrieval-test-key",
				Algorithm: "AES-256",
				KeyType:   crypto.SymmetricKey,
				Usage:     []crypto.KeyUsage{crypto.UsageEncrypt, crypto.UsageDecrypt},
			}

			storageOpts := &crypto.KeyStorageOptions{
				Password: keyPassword,
			}

			keyEntry, err = keystore.GenerateKey(opts, storageOpts)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when retrieving with correct password", func() {
			It("should retrieve the key successfully", func() {
				storageOpts := &crypto.KeyStorageOptions{
					Password: keyPassword,
				}

				entry, keyData, err := keystore.GetKey(keyEntry.ID, storageOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(entry).NotTo(BeNil())
				Expect(entry.ID).To(Equal(keyEntry.ID))
				Expect(keyData).To(HaveLen(32)) // 256-bit key
			})
		})

		Context("when retrieving with wrong password", func() {
			It("should return an error", func() {
				storageOpts := &crypto.KeyStorageOptions{
					Password: "wrong-password",
				}

				_, _, err := keystore.GetKey(keyEntry.ID, storageOpts)
				Expect(err).To(HaveOccurred())
			})
		})

		Context("when retrieving non-existent key", func() {
			It("should return an error", func() {
				storageOpts := &crypto.KeyStorageOptions{
					Password: keyPassword,
				}

				_, _, err := keystore.GetKey("non-existent-key", storageOpts)
				Expect(err).To(HaveOccurred())
			})
		})

		Context("when retrieving revoked key", func() {
			BeforeEach(func() {
				err := keystore.RevokeKey(keyEntry.ID)
				Expect(err).NotTo(HaveOccurred())
			})

			It("should return an error", func() {
				storageOpts := &crypto.KeyStorageOptions{
					Password: keyPassword,
				}

				_, _, err := keystore.GetKey(keyEntry.ID, storageOpts)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("key is revoked"))
			})
		})
	})

	Describe("Key listing", func() {
		BeforeEach(func() {
			var err error
			keystore, err = crypto.NewKeyStore(cfg)
			Expect(err).NotTo(HaveOccurred())

			// Generate multiple keys for testing
			for i := 0; i < 3; i++ {
				opts := &crypto.KeyGenerationOptions{
					Name:      "list-test-key-" + string(rune('A'+i)),
					Algorithm: "AES-256",
					KeyType:   crypto.SymmetricKey,
					Usage:     []crypto.KeyUsage{crypto.UsageEncrypt},
				}

				storageOpts := &crypto.KeyStorageOptions{
					Password: "list-test-password",
				}

				_, err := keystore.GenerateKey(opts, storageOpts)
				Expect(err).NotTo(HaveOccurred())
			}
		})

		Context("when listing keys", func() {
			It("should return all generated keys", func() {
				entries, err := keystore.ListKeys()
				Expect(err).NotTo(HaveOccurred())
				Expect(entries).To(HaveLen(3))

				for _, entry := range entries {
					Expect(entry.Name).To(MatchRegexp("list-test-key-[A-C]"))
					Expect(entry.Algorithm).To(Equal("AES-256"))
				}
			})
		})
	})

	Describe("Key rotation", func() {
		var keyEntry *crypto.KeyEntry
		var keyPassword string

		BeforeEach(func() {
			var err error
			keystore, err = crypto.NewKeyStore(cfg)
			Expect(err).NotTo(HaveOccurred())

			keyPassword = "rotation-test-password"
			opts := &crypto.KeyGenerationOptions{
				Name:      "rotation-test-key",
				Algorithm: "AES-256",
				KeyType:   crypto.SymmetricKey,
				Usage:     []crypto.KeyUsage{crypto.UsageEncrypt, crypto.UsageDecrypt},
			}

			storageOpts := &crypto.KeyStorageOptions{
				Password: keyPassword,
			}

			keyEntry, err = keystore.GenerateKey(opts, storageOpts)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when rotating a key", func() {
			It("should create a new key and revoke the old one", func() {
				storageOpts := &crypto.KeyStorageOptions{
					Password: keyPassword,
				}

				newEntry, err := keystore.RotateKey(keyEntry.ID, storageOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(newEntry).NotTo(BeNil())
				Expect(newEntry.ID).NotTo(Equal(keyEntry.ID))
				Expect(newEntry.Name).To(ContainSubstring("(rotated)"))
				Expect(newEntry.Algorithm).To(Equal(keyEntry.Algorithm))
				Expect(newEntry.KeyType).To(Equal(keyEntry.KeyType))

				// Verify old key is revoked
				_, _, err = keystore.GetKey(keyEntry.ID, storageOpts)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("key is revoked"))

				// Verify new key works
				_, keyData, err := keystore.GetKey(newEntry.ID, storageOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(keyData).To(HaveLen(32))
			})
		})
	})

	Describe("Key export and import", func() {
		var keyEntry *crypto.KeyEntry
		var keyPassword string

		BeforeEach(func() {
			var err error
			keystore, err = crypto.NewKeyStore(cfg)
			Expect(err).NotTo(HaveOccurred())

			keyPassword = "export-test-password"
			opts := &crypto.KeyGenerationOptions{
				Name:      "export-test-key",
				Algorithm: "AES-256",
				KeyType:   crypto.SymmetricKey,
				Usage:     []crypto.KeyUsage{crypto.UsageEncrypt, crypto.UsageDecrypt},
			}

			storageOpts := &crypto.KeyStorageOptions{
				Password: keyPassword,
			}

			keyEntry, err = keystore.GenerateKey(opts, storageOpts)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when exporting and importing a key", func() {
			It("should export and import successfully", func() {
				storageOpts := &crypto.KeyStorageOptions{
					Password: keyPassword,
				}

				exportPassword := "export-password-123"

				// Export key
				exportData, err := keystore.ExportKey(keyEntry.ID, storageOpts, exportPassword)
				Expect(err).NotTo(HaveOccurred())
				Expect(exportData).NotTo(BeEmpty())

				// Import key
				importedEntry, err := keystore.ImportKey(exportData, exportPassword, storageOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(importedEntry).NotTo(BeNil())
				Expect(importedEntry.ID).NotTo(Equal(keyEntry.ID)) // New ID assigned
				Expect(importedEntry.Name).To(Equal(keyEntry.Name))
				Expect(importedEntry.Algorithm).To(Equal(keyEntry.Algorithm))
				Expect(importedEntry.KeyType).To(Equal(keyEntry.KeyType))

				// Verify imported key works
				_, keyData, err := keystore.GetKey(importedEntry.ID, storageOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(keyData).To(HaveLen(32))
			})
		})

		Context("when importing with wrong export password", func() {
			It("should return an error", func() {
				storageOpts := &crypto.KeyStorageOptions{
					Password: keyPassword,
				}

				exportPassword := "export-password-123"
				wrongPassword := "wrong-export-password"

				// Export key
				exportData, err := keystore.ExportKey(keyEntry.ID, storageOpts, exportPassword)
				Expect(err).NotTo(HaveOccurred())

				// Try to import with wrong password
				_, err = keystore.ImportKey(exportData, wrongPassword, storageOpts)
				Expect(err).To(HaveOccurred())
			})
		})
	})

	Describe("Key deletion", func() {
		var keyEntry *crypto.KeyEntry

		BeforeEach(func() {
			var err error
			keystore, err = crypto.NewKeyStore(cfg)
			Expect(err).NotTo(HaveOccurred())

			opts := &crypto.KeyGenerationOptions{
				Name:      "deletion-test-key",
				Algorithm: "AES-256",
				KeyType:   crypto.SymmetricKey,
				Usage:     []crypto.KeyUsage{crypto.UsageEncrypt},
			}

			storageOpts := &crypto.KeyStorageOptions{
				Password: "deletion-test-password",
			}

			keyEntry, err = keystore.GenerateKey(opts, storageOpts)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when deleting a key", func() {
			It("should delete the key permanently", func() {
				err := keystore.DeleteKey(keyEntry.ID)
				Expect(err).NotTo(HaveOccurred())

				// Verify key is gone
				storageOpts := &crypto.KeyStorageOptions{
					Password: "deletion-test-password",
				}

				_, _, err = keystore.GetKey(keyEntry.ID, storageOpts)
				Expect(err).To(HaveOccurred())
			})
		})
	})

	Describe("Health checks", func() {
		Context("when keystore is healthy", func() {
			BeforeEach(func() {
				var err error
				keystore, err = crypto.NewKeyStore(cfg)
				Expect(err).NotTo(HaveOccurred())
			})

			It("should pass health check", func() {
				err := keystore.Health()
				Expect(err).NotTo(HaveOccurred())
			})
		})

		Context("when keystore is disabled", func() {
			BeforeEach(func() {
				cfg.Security.KeyStore.Enabled = false
			})

			It("should fail creation", func() {
				_, err := crypto.NewKeyStore(cfg)
				Expect(err).To(HaveOccurred())
			})
		})
	})
})

// Traditional unit tests using testify
type KeyStoreTestSuite struct {
	suite.Suite
	config   *config.Config
	keystore *crypto.KeyStore
	tempDir  string
}

func (suite *KeyStoreTestSuite) SetupTest() {
	var err error
	suite.tempDir, err = os.MkdirTemp("", "keystore-suite-test-*")
	suite.Require().NoError(err)

	suite.config = &config.Config{
		Security: config.SecurityConfig{
			Encryption: config.EncryptionConfig{
				Enabled: true,
			},
			KeyStore: config.KeyStoreConfig{
				Enabled: true,
				Path:    suite.tempDir,
			},
		},
	}

	suite.keystore, err = crypto.NewKeyStore(suite.config)
	suite.Require().NoError(err)
}

func (suite *KeyStoreTestSuite) TearDownTest() {
	if suite.tempDir != "" {
		os.RemoveAll(suite.tempDir)
	}
}

func (suite *KeyStoreTestSuite) TestGenerateKey_Success() {
	opts := &crypto.KeyGenerationOptions{
		Name:      "test-key",
		Algorithm: "AES-256",
		KeyType:   crypto.SymmetricKey,
		Usage:     []crypto.KeyUsage{crypto.UsageEncrypt},
	}

	storageOpts := &crypto.KeyStorageOptions{
		Password: "test-password",
	}

	entry, err := suite.keystore.GenerateKey(opts, storageOpts)
	suite.NoError(err)
	suite.NotNil(entry)
	suite.NotEmpty(entry.ID)
	suite.Equal("test-key", entry.Name)
}

func (suite *KeyStoreTestSuite) TestGetKey_Success() {
	// Generate a key first
	opts := &crypto.KeyGenerationOptions{
		Name:      "get-test-key",
		Algorithm: "AES-256",
		KeyType:   crypto.SymmetricKey,
		Usage:     []crypto.KeyUsage{crypto.UsageEncrypt},
	}

	storageOpts := &crypto.KeyStorageOptions{
		Password: "get-test-password",
	}

	entry, err := suite.keystore.GenerateKey(opts, storageOpts)
	suite.Require().NoError(err)

	// Retrieve the key
	retrievedEntry, keyData, err := suite.keystore.GetKey(entry.ID, storageOpts)
	suite.NoError(err)
	suite.NotNil(retrievedEntry)
	suite.Equal(entry.ID, retrievedEntry.ID)
	suite.Len(keyData, 32)
}

func (suite *KeyStoreTestSuite) TestHealth_Success() {
	err := suite.keystore.Health()
	suite.NoError(err)
}

func TestKeyStoreSuite(t *testing.T) {
	suite.Run(t, new(KeyStoreTestSuite))
}