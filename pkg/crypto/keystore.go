// Package: crypto
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:keystore-impl:2025-01-27T10:00:00.123Z
// 
// Purpose: Secure key storage and management with post-quantum preparation
// Governance: MCStack v13.5 compliance with enterprise key management standards
// Security: Hardware Security Module (HSM) integration ready, encrypted key storage
package crypto

import (
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/sirupsen/logrus"
)

// KeyStore provides secure key storage and management
type KeyStore struct {
	config   *config.Config
	keyPath  string
	cipher   *Cipher
}

// KeyEntry represents a stored key
type KeyEntry struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Algorithm   string            `json:"algorithm"`
	KeyType     string            `json:"key_type"`
	Created     time.Time         `json:"created"`
	Modified    time.Time         `json:"modified"`
	ExpiresAt   *time.Time        `json:"expires_at,omitempty"`
	Revoked     bool              `json:"revoked"`
	RevokedAt   *time.Time        `json:"revoked_at,omitempty"`
	Usage       []string          `json:"usage"`
	Metadata    map[string]string `json:"metadata,omitempty"`
	EncryptedKey *EncryptionResult `json:"encrypted_key"`
}

// KeyType represents the type of cryptographic key
type KeyType string

const (
	// SymmetricKey represents a symmetric encryption key
	SymmetricKey KeyType = "symmetric"
	// PrivateKey represents an asymmetric private key
	PrivateKey KeyType = "private"
	// PublicKey represents an asymmetric public key
	PublicKey KeyType = "public"
	// SigningKey represents a key used for digital signatures
	SigningKey KeyType = "signing"
	// MasterKey represents a key encryption key
	MasterKey KeyType = "master"
)

// KeyUsage represents allowed key usage
type KeyUsage string

const (
	// UsageEncrypt allows key to be used for encryption
	UsageEncrypt KeyUsage = "encrypt"
	// UsageDecrypt allows key to be used for decryption
	UsageDecrypt KeyUsage = "decrypt"
	// UsageSign allows key to be used for signing
	UsageSign KeyUsage = "sign"
	// UsageVerify allows key to be used for verification
	UsageVerify KeyUsage = "verify"
	// UsageKeyWrap allows key to be used for key wrapping
	UsageKeyWrap KeyUsage = "key_wrap"
	// UsageKeyUnwrap allows key to be used for key unwrapping
	UsageKeyUnwrap KeyUsage = "key_unwrap"
)

// KeyGenerationOptions represents options for key generation
type KeyGenerationOptions struct {
	Name      string
	Algorithm string
	KeyType   KeyType
	Usage     []KeyUsage
	ExpiresAt *time.Time
	Metadata  map[string]string
}

// KeyStorageOptions represents options for key storage
type KeyStorageOptions struct {
	Password  string
	MasterKey []byte
}

// NewKeyStore creates a new key store instance
func NewKeyStore(cfg *config.Config) (*KeyStore, error) {
	logrus.WithFields(logrus.Fields{
		"path":    cfg.Security.KeyStore.Path,
		"enabled": cfg.Security.KeyStore.Enabled,
	}).Info("Creating crypto keystore")

	if !cfg.Security.KeyStore.Enabled {
		return nil, fmt.Errorf("keystore is disabled in configuration")
	}

	// Create keystore directory if it doesn't exist
	keyPath := cfg.Security.KeyStore.Path
	if keyPath == "" {
		keyPath = "keys"
	}

	if err := os.MkdirAll(keyPath, 0700); err != nil {
		return nil, fmt.Errorf("failed to create keystore directory: %w", err)
	}

	// Create cipher for key encryption
	cipher, err := NewCipher(cfg, XChaCha20Poly1305)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher for keystore: %w", err)
	}

	keystore := &KeyStore{
		config:  cfg,
		keyPath: keyPath,
		cipher:  cipher,
	}

	logrus.Info("Crypto keystore created successfully")
	return keystore, nil
}

// GenerateKey generates a new cryptographic key
func (ks *KeyStore) GenerateKey(opts *KeyGenerationOptions, storageOpts *KeyStorageOptions) (*KeyEntry, error) {
	logrus.WithFields(logrus.Fields{
		"name":      opts.Name,
		"algorithm": opts.Algorithm,
		"key_type":  string(opts.KeyType),
		"usage":     opts.Usage,
	}).Info("Generating new cryptographic key")

	// Generate key ID
	keyID := ks.generateKeyID()

	// Generate the actual key material
	var keyData []byte
	var err error

	switch opts.KeyType {
	case SymmetricKey:
		keyData, err = ks.generateSymmetricKey(opts.Algorithm)
	case MasterKey:
		keyData, err = ks.generateMasterKey()
	default:
		return nil, fmt.Errorf("unsupported key type for generation: %s", opts.KeyType)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to generate key material: %w", err)
	}

	// Encrypt the key material
	encOpts := &EncryptionOptions{}
	if storageOpts.Password != "" {
		encOpts.Password = storageOpts.Password
	} else if len(storageOpts.MasterKey) > 0 {
		encOpts.KeyData = storageOpts.MasterKey
	} else {
		return nil, fmt.Errorf("either password or master key must be provided for key storage")
	}

	encryptedKey, err := ks.cipher.Encrypt(keyData, encOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt key material: %w", err)
	}

	// Convert usage slice
	usageStrs := make([]string, len(opts.Usage))
	for i, usage := range opts.Usage {
		usageStrs[i] = string(usage)
	}

	// Create key entry
	entry := &KeyEntry{
		ID:           keyID,
		Name:         opts.Name,
		Algorithm:    opts.Algorithm,
		KeyType:      string(opts.KeyType),
		Created:      time.Now(),
		Modified:     time.Now(),
		ExpiresAt:    opts.ExpiresAt,
		Revoked:      false,
		Usage:        usageStrs,
		Metadata:     opts.Metadata,
		EncryptedKey: encryptedKey,
	}

	// Store the key
	if err := ks.storeKey(entry); err != nil {
		return nil, fmt.Errorf("failed to store key: %w", err)
	}

	logrus.WithField("key_id", keyID).Info("Key generated and stored successfully")
	return entry, nil
}

// GetKey retrieves a key by ID
func (ks *KeyStore) GetKey(keyID string, storageOpts *KeyStorageOptions) (*KeyEntry, []byte, error) {
	logrus.WithField("key_id", keyID).Debug("Retrieving key from keystore")

	// Load key entry
	entry, err := ks.loadKey(keyID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to load key: %w", err)
	}

	// Check if key is revoked
	if entry.Revoked {
		return nil, nil, fmt.Errorf("key is revoked")
	}

	// Check if key is expired
	if entry.ExpiresAt != nil && time.Now().After(*entry.ExpiresAt) {
		return nil, nil, fmt.Errorf("key is expired")
	}

	// Decrypt key material
	decOpts := &DecryptionOptions{}
	if storageOpts.Password != "" {
		decOpts.Password = storageOpts.Password
	} else if len(storageOpts.MasterKey) > 0 {
		decOpts.KeyData = storageOpts.MasterKey
	} else {
		return nil, nil, fmt.Errorf("either password or master key must be provided for key retrieval")
	}

	keyData, err := ks.cipher.Decrypt(entry.EncryptedKey, decOpts)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to decrypt key material: %w", err)
	}

	return entry, keyData, nil
}

// ListKeys lists all keys in the keystore
func (ks *KeyStore) ListKeys() ([]*KeyEntry, error) {
	logrus.Debug("Listing keys in keystore")

	files, err := os.ReadDir(ks.keyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read keystore directory: %w", err)
	}

	var entries []*KeyEntry
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".key" {
			continue
		}

		keyID := file.Name()[:len(file.Name())-4] // Remove .key extension
		entry, err := ks.loadKey(keyID)
		if err != nil {
			logrus.WithError(err).WithField("key_id", keyID).Warn("Failed to load key")
			continue
		}

		entries = append(entries, entry)
	}

	return entries, nil
}

// RevokeKey revokes a key
func (ks *KeyStore) RevokeKey(keyID string) error {
	logrus.WithField("key_id", keyID).Info("Revoking key")

	// Load key entry
	entry, err := ks.loadKey(keyID)
	if err != nil {
		return fmt.Errorf("failed to load key: %w", err)
	}

	// Mark as revoked
	now := time.Now()
	entry.Revoked = true
	entry.RevokedAt = &now
	entry.Modified = now

	// Store updated entry
	if err := ks.storeKey(entry); err != nil {
		return fmt.Errorf("failed to store revoked key: %w", err)
	}

	logrus.WithField("key_id", keyID).Info("Key revoked successfully")
	return nil
}

// DeleteKey permanently deletes a key
func (ks *KeyStore) DeleteKey(keyID string) error {
	logrus.WithField("key_id", keyID).Info("Deleting key permanently")

	keyFile := filepath.Join(ks.keyPath, keyID+".key")
	if err := os.Remove(keyFile); err != nil {
		return fmt.Errorf("failed to delete key file: %w", err)
	}

	logrus.WithField("key_id", keyID).Info("Key deleted successfully")
	return nil
}

// RotateKey creates a new version of an existing key
func (ks *KeyStore) RotateKey(keyID string, storageOpts *KeyStorageOptions) (*KeyEntry, error) {
	logrus.WithField("key_id", keyID).Info("Rotating key")

	// Get existing key
	oldEntry, _, err := ks.GetKey(keyID, storageOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing key: %w", err)
	}

	// Create new key with same properties
	opts := &KeyGenerationOptions{
		Name:      oldEntry.Name + " (rotated)",
		Algorithm: oldEntry.Algorithm,
		KeyType:   KeyType(oldEntry.KeyType),
		Usage:     make([]KeyUsage, len(oldEntry.Usage)),
		ExpiresAt: oldEntry.ExpiresAt,
		Metadata:  oldEntry.Metadata,
	}

	// Convert usage strings back to KeyUsage
	for i, usage := range oldEntry.Usage {
		opts.Usage[i] = KeyUsage(usage)
	}

	// Generate new key
	newEntry, err := ks.GenerateKey(opts, storageOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to generate rotated key: %w", err)
	}

	// Revoke old key
	if err := ks.RevokeKey(keyID); err != nil {
		logrus.WithError(err).Warn("Failed to revoke old key after rotation")
	}

	logrus.WithFields(logrus.Fields{
		"old_key_id": keyID,
		"new_key_id": newEntry.ID,
	}).Info("Key rotated successfully")

	return newEntry, nil
}

// ExportKey exports a key in encrypted format
func (ks *KeyStore) ExportKey(keyID string, storageOpts *KeyStorageOptions, exportPassword string) ([]byte, error) {
	logrus.WithField("key_id", keyID).Info("Exporting key")

	// Get key
	entry, keyData, err := ks.GetKey(keyID, storageOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to get key: %w", err)
	}

	// Create export package
	exportPackage := map[string]interface{}{
		"entry":    entry,
		"key_data": base64.StdEncoding.EncodeToString(keyData),
		"exported": time.Now(),
	}

	// Serialize export package
	packageData, err := json.Marshal(exportPackage)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize export package: %w", err)
	}

	// Encrypt with export password
	exportOpts := &EncryptionOptions{
		Password: exportPassword,
	}

	result, err := ks.cipher.Encrypt(packageData, exportOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt export package: %w", err)
	}

	// Serialize encrypted result
	exportData, err := json.Marshal(result)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize encrypted export: %w", err)
	}

	logrus.WithField("key_id", keyID).Info("Key exported successfully")
	return exportData, nil
}

// ImportKey imports a key from encrypted format
func (ks *KeyStore) ImportKey(exportData []byte, exportPassword string, storageOpts *KeyStorageOptions) (*KeyEntry, error) {
	logrus.Info("Importing key")

	// Deserialize encrypted result
	var result EncryptionResult
	if err := json.Unmarshal(exportData, &result); err != nil {
		return nil, fmt.Errorf("failed to deserialize encrypted export: %w", err)
	}

	// Decrypt with export password
	decOpts := &DecryptionOptions{
		Password: exportPassword,
	}

	packageData, err := ks.cipher.Decrypt(&result, decOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt export package: %w", err)
	}

	// Deserialize export package
	var exportPackage map[string]interface{}
	if err := json.Unmarshal(packageData, &exportPackage); err != nil {
		return nil, fmt.Errorf("failed to deserialize export package: %w", err)
	}

	// Extract entry and key data
	entryData, _ := json.Marshal(exportPackage["entry"])
	var entry KeyEntry
	if err := json.Unmarshal(entryData, &entry); err != nil {
		return nil, fmt.Errorf("failed to deserialize key entry: %w", err)
	}

	keyDataStr, _ := exportPackage["key_data"].(string)
	keyData, err := base64.StdEncoding.DecodeString(keyDataStr)
	if err != nil {
		return nil, fmt.Errorf("failed to decode key data: %w", err)
	}

	// Generate new key ID for imported key
	entry.ID = ks.generateKeyID()
	entry.Created = time.Now()
	entry.Modified = time.Now()

	// Re-encrypt with storage options
	encOpts := &EncryptionOptions{}
	if storageOpts.Password != "" {
		encOpts.Password = storageOpts.Password
	} else if len(storageOpts.MasterKey) > 0 {
		encOpts.KeyData = storageOpts.MasterKey
	} else {
		return nil, fmt.Errorf("either password or master key must be provided for key storage")
	}

	encryptedKey, err := ks.cipher.Encrypt(keyData, encOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt imported key: %w", err)
	}

	entry.EncryptedKey = encryptedKey

	// Store the imported key
	if err := ks.storeKey(&entry); err != nil {
		return nil, fmt.Errorf("failed to store imported key: %w", err)
	}

	logrus.WithField("key_id", entry.ID).Info("Key imported successfully")
	return &entry, nil
}

// Health checks the health of the keystore
func (ks *KeyStore) Health() error {
	logrus.Debug("Checking keystore health")

	if !ks.config.Security.KeyStore.Enabled {
		return fmt.Errorf("keystore is disabled")
	}

	// Check keystore directory
	if _, err := os.Stat(ks.keyPath); os.IsNotExist(err) {
		return fmt.Errorf("keystore directory does not exist: %s", ks.keyPath)
	}

	// Check cipher health
	if err := ks.cipher.Health(); err != nil {
		return fmt.Errorf("cipher health check failed: %w", err)
	}

	// Test key operations with a temporary key
	testOpts := &KeyGenerationOptions{
		Name:      "health-check-test",
		Algorithm: "AES-256",
		KeyType:   SymmetricKey,
		Usage:     []KeyUsage{UsageEncrypt, UsageDecrypt},
	}

	testStorageOpts := &KeyStorageOptions{
		Password: "test-password-12345",
	}

	// Generate test key
	entry, err := ks.GenerateKey(testOpts, testStorageOpts)
	if err != nil {
		return fmt.Errorf("failed to generate test key: %w", err)
	}

	// Retrieve test key
	_, _, err = ks.GetKey(entry.ID, testStorageOpts)
	if err != nil {
		return fmt.Errorf("failed to retrieve test key: %w", err)
	}

	// Clean up test key
	if err := ks.DeleteKey(entry.ID); err != nil {
		logrus.WithError(err).Warn("Failed to clean up test key")
	}

	return nil
}

// Helper methods

func (ks *KeyStore) generateKeyID() string {
	// Generate a random 16-byte ID and encode as hex
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback to timestamp-based ID
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

func (ks *KeyStore) generateSymmetricKey(algorithm string) ([]byte, error) {
	switch algorithm {
	case "AES-256", "ChaCha20", "XChaCha20":
		// 256-bit key
		key := make([]byte, 32)
		if _, err := rand.Read(key); err != nil {
			return nil, fmt.Errorf("failed to generate symmetric key: %w", err)
		}
		return key, nil
	case "AES-128":
		// 128-bit key
		key := make([]byte, 16)
		if _, err := rand.Read(key); err != nil {
			return nil, fmt.Errorf("failed to generate symmetric key: %w", err)
		}
		return key, nil
	default:
		return nil, fmt.Errorf("unsupported symmetric algorithm: %s", algorithm)
	}
}

func (ks *KeyStore) generateMasterKey() ([]byte, error) {
	// Generate a 256-bit master key
	key := make([]byte, 32)
	if _, err := rand.Read(key); err != nil {
		return nil, fmt.Errorf("failed to generate master key: %w", err)
	}
	return key, nil
}

func (ks *KeyStore) storeKey(entry *KeyEntry) error {
	keyFile := filepath.Join(ks.keyPath, entry.ID+".key")
	data, err := json.MarshalIndent(entry, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal key entry: %w", err)
	}

	if err := os.WriteFile(keyFile, data, 0600); err != nil {
		return fmt.Errorf("failed to write key file: %w", err)
	}

	return nil
}

func (ks *KeyStore) loadKey(keyID string) (*KeyEntry, error) {
	keyFile := filepath.Join(ks.keyPath, keyID+".key")
	data, err := os.ReadFile(keyFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read key file: %w", err)
	}

	var entry KeyEntry
	if err := json.Unmarshal(data, &entry); err != nil {
		return nil, fmt.Errorf("failed to unmarshal key entry: %w", err)
	}

	return &entry, nil
}