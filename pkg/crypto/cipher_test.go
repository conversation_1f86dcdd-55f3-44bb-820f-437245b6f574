// Package: crypto_test
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:crypto-test:2025-01-27T10:00:00.123Z
// 
// Purpose: BDD tests for crypto package with post-quantum encryption
// Governance: MCStack v13.5 test compliance with comprehensive coverage
// Security: Test cryptographic operations with known test vectors
package crypto_test

import (
	"testing"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/suite"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/mchorfa/mc-poly-installer/pkg/crypto"
)

func TestCrypto(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Crypto Package Suite")
}

var _ = Describe("Crypto Cipher", func() {
	var (
		cfg    *config.Config
		cipher *crypto.Cipher
	)

	BeforeEach(func() {
		cfg = &config.Config{
			Security: config.SecurityConfig{
				Encryption: config.EncryptionConfig{
					Enabled:   true,
					Algorithm: "aes-256-gcm",
				},
			},
		}
	})

	Describe("Creating a new cipher", func() {
		Context("when encryption is enabled", func() {
			It("should create a cipher for AES-256-GCM", func() {
				var err error
				cipher, err = crypto.NewCipher(cfg, crypto.AES256GCM)
				Expect(err).NotTo(HaveOccurred())
				Expect(cipher).NotTo(BeNil())
			})

			It("should create a cipher for ChaCha20-Poly1305", func() {
				var err error
				cipher, err = crypto.NewCipher(cfg, crypto.ChaCha20Poly1305)
				Expect(err).NotTo(HaveOccurred())
				Expect(cipher).NotTo(BeNil())
			})

			It("should create a cipher for XChaCha20-Poly1305", func() {
				var err error
				cipher, err = crypto.NewCipher(cfg, crypto.XChaCha20Poly1305)
				Expect(err).NotTo(HaveOccurred())
				Expect(cipher).NotTo(BeNil())
			})
		})

		Context("when encryption is disabled", func() {
			BeforeEach(func() {
				cfg.Security.Encryption.Enabled = false
			})

			It("should return an error", func() {
				_, err := crypto.NewCipher(cfg, crypto.AES256GCM)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("encryption is disabled"))
			})
		})

		Context("when cipher suite is unsupported", func() {
			It("should return an error", func() {
				_, err := crypto.NewCipher(cfg, crypto.CipherSuite("invalid-cipher"))
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("unsupported cipher suite"))
			})
		})
	})

	Describe("Key generation", func() {
		BeforeEach(func() {
			var err error
			cipher, err = crypto.NewCipher(cfg, crypto.AES256GCM)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when generating a key", func() {
			It("should generate a 256-bit key", func() {
				key, err := cipher.GenerateKey()
				Expect(err).NotTo(HaveOccurred())
				Expect(key).To(HaveLen(32)) // 256 bits = 32 bytes
			})

			It("should generate different keys each time", func() {
				key1, err := cipher.GenerateKey()
				Expect(err).NotTo(HaveOccurred())

				key2, err := cipher.GenerateKey()
				Expect(err).NotTo(HaveOccurred())

				Expect(key1).NotTo(Equal(key2))
			})
		})
	})

	Describe("Password-based encryption", func() {
		BeforeEach(func() {
			var err error
			cipher, err = crypto.NewCipher(cfg, crypto.AES256GCM)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when encrypting with password", func() {
			It("should encrypt and decrypt successfully", func() {
				testData := []byte("Hello, MCStack v13.5!")
				password := "test-password-123"

				opts := &crypto.EncryptionOptions{
					Password: password,
				}

				// Encrypt
				result, err := cipher.Encrypt(testData, opts)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).NotTo(BeNil())
				Expect(result.Ciphertext).NotTo(BeEmpty())
				Expect(result.Nonce).NotTo(BeEmpty())
				Expect(result.Salt).NotTo(BeEmpty())
				Expect(result.Algorithm).To(Equal(string(crypto.AES256GCM)))

				// Decrypt
				decOpts := &crypto.DecryptionOptions{
					Password: password,
				}

				decrypted, err := cipher.Decrypt(result, decOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(decrypted).To(Equal(testData))
			})

			It("should fail with wrong password", func() {
				testData := []byte("Hello, MCStack v13.5!")
				password := "test-password-123"
				wrongPassword := "wrong-password"

				opts := &crypto.EncryptionOptions{
					Password: password,
				}

				// Encrypt
				result, err := cipher.Encrypt(testData, opts)
				Expect(err).NotTo(HaveOccurred())

				// Try to decrypt with wrong password
				decOpts := &crypto.DecryptionOptions{
					Password: wrongPassword,
				}

				_, err = cipher.Decrypt(result, decOpts)
				Expect(err).To(HaveOccurred())
			})
		})

		Context("when encrypting with custom iterations", func() {
			It("should use custom PBKDF2 iterations", func() {
				testData := []byte("Test data")
				password := "test-password"

				opts := &crypto.EncryptionOptions{
					Password:   password,
					Iterations: 50000,
				}

				result, err := cipher.Encrypt(testData, opts)
				Expect(err).NotTo(HaveOccurred())
				Expect(result.Metadata["pbkdf2_iterations"]).To(Equal("50000"))
			})
		})
	})

	Describe("Key-based encryption", func() {
		BeforeEach(func() {
			var err error
			cipher, err = crypto.NewCipher(cfg, crypto.ChaCha20Poly1305)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when encrypting with key", func() {
			It("should encrypt and decrypt successfully", func() {
				testData := []byte("Key-based encryption test")
				key, err := cipher.GenerateKey()
				Expect(err).NotTo(HaveOccurred())

				opts := &crypto.EncryptionOptions{
					KeyData: key,
				}

				// Encrypt
				result, err := cipher.Encrypt(testData, opts)
				Expect(err).NotTo(HaveOccurred())
				Expect(result.Salt).To(BeEmpty()) // No salt for key-based encryption

				// Decrypt
				decOpts := &crypto.DecryptionOptions{
					KeyData: key,
				}

				decrypted, err := cipher.Decrypt(result, decOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(decrypted).To(Equal(testData))
			})

			It("should fail with wrong key", func() {
				testData := []byte("Key-based encryption test")
				key, err := cipher.GenerateKey()
				Expect(err).NotTo(HaveOccurred())

				wrongKey, err := cipher.GenerateKey()
				Expect(err).NotTo(HaveOccurred())

				opts := &crypto.EncryptionOptions{
					KeyData: key,
				}

				// Encrypt
				result, err := cipher.Encrypt(testData, opts)
				Expect(err).NotTo(HaveOccurred())

				// Try to decrypt with wrong key
				decOpts := &crypto.DecryptionOptions{
					KeyData: wrongKey,
				}

				_, err = cipher.Decrypt(result, decOpts)
				Expect(err).To(HaveOccurred())
			})

			It("should fail with incorrect key size", func() {
				testData := []byte("Test data")
				shortKey := []byte("short")

				opts := &crypto.EncryptionOptions{
					KeyData: shortKey,
				}

				_, err := cipher.Encrypt(testData, opts)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("key must be 32 bytes"))
			})
		})
	})

	Describe("String encryption", func() {
		BeforeEach(func() {
			var err error
			cipher, err = crypto.NewCipher(cfg, crypto.XChaCha20Poly1305)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when encrypting strings", func() {
			It("should encrypt and decrypt strings successfully", func() {
				testString := "Hello, MCStack v13.5 String Encryption!"
				password := "string-password"

				opts := &crypto.EncryptionOptions{
					Password: password,
				}

				// Encrypt string
				result, err := cipher.EncryptString(testString, opts)
				Expect(err).NotTo(HaveOccurred())

				// Decrypt string
				decOpts := &crypto.DecryptionOptions{
					Password: password,
				}

				decrypted, err := cipher.DecryptString(result, decOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(decrypted).To(Equal(testString))
			})
		})
	})

	Describe("Associated data", func() {
		BeforeEach(func() {
			var err error
			cipher, err = crypto.NewCipher(cfg, crypto.AES256GCM)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when using associated data", func() {
			It("should include associated data in authentication", func() {
				testData := []byte("Authenticated encryption test")
				associatedData := []byte("metadata")
				key, err := cipher.GenerateKey()
				Expect(err).NotTo(HaveOccurred())

				opts := &crypto.EncryptionOptions{
					KeyData:        key,
					AssociatedData: associatedData,
				}

				// Encrypt
				result, err := cipher.Encrypt(testData, opts)
				Expect(err).NotTo(HaveOccurred())

				// Decrypt with correct associated data
				decOpts := &crypto.DecryptionOptions{
					KeyData: key,
				}

				decrypted, err := cipher.Decrypt(result, decOpts)
				Expect(err).NotTo(HaveOccurred())
				Expect(decrypted).To(Equal(testData))
			})
		})
	})

	Describe("Health checks", func() {
		BeforeEach(func() {
			var err error
			cipher, err = crypto.NewCipher(cfg, crypto.AES256GCM)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when cipher is healthy", func() {
			It("should pass health check", func() {
				err := cipher.Health()
				Expect(err).NotTo(HaveOccurred())
			})
		})

		Context("when encryption is disabled", func() {
			BeforeEach(func() {
				cfg.Security.Encryption.Enabled = false
			})

			It("should fail health check", func() {
				cipher, err := crypto.NewCipher(cfg, crypto.AES256GCM)
				Expect(err).To(HaveOccurred())
				Expect(cipher).To(BeNil())
			})
		})
	})
})

// Traditional unit tests using testify
type CipherTestSuite struct {
	suite.Suite
	config *config.Config
	cipher *crypto.Cipher
}

func (suite *CipherTestSuite) SetupTest() {
	suite.config = &config.Config{
		Security: config.SecurityConfig{
			Encryption: config.EncryptionConfig{
				Enabled:   true,
				Algorithm: "aes-256-gcm",
			},
		},
	}

	var err error
	suite.cipher, err = crypto.NewCipher(suite.config, crypto.AES256GCM)
	suite.Require().NoError(err)
}

func (suite *CipherTestSuite) TestEncryptDecrypt_Success() {
	testData := []byte("Test data for encryption")
	key, err := suite.cipher.GenerateKey()
	suite.Require().NoError(err)

	opts := &crypto.EncryptionOptions{
		KeyData: key,
	}

	result, err := suite.cipher.Encrypt(testData, opts)
	suite.NoError(err)
	suite.NotNil(result)
	suite.NotEmpty(result.Ciphertext)

	decOpts := &crypto.DecryptionOptions{
		KeyData: key,
	}

	decrypted, err := suite.cipher.Decrypt(result, decOpts)
	suite.NoError(err)
	suite.Equal(testData, decrypted)
}

func (suite *CipherTestSuite) TestEncrypt_NoKeyOrPassword() {
	testData := []byte("Test data")
	opts := &crypto.EncryptionOptions{}

	_, err := suite.cipher.Encrypt(testData, opts)
	suite.Error(err)
	suite.Contains(err.Error(), "either password or key data must be provided")
}

func (suite *CipherTestSuite) TestHealth_Success() {
	err := suite.cipher.Health()
	suite.NoError(err)
}

func TestCipherSuite(t *testing.T) {
	suite.Run(t, new(CipherTestSuite))
}