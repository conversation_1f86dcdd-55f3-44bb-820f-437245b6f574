// Package: compliance_test
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:audit-test:2025-01-27T10:00:00.123Z
// 
// Purpose: BDD tests for compliance audit trail functionality
// Governance: MCStack v13.5 test compliance with comprehensive coverage
// Security: Test audit operations with tamper-evident verification
package compliance_test

import (
	"os"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/suite"

	"github.com/mchorfa/mc-poly-installer/pkg/compliance"
	"github.com/mchorfa/mc-poly-installer/pkg/config"
)

var _ = Describe("Audit Trail", func() {
	var (
		cfg        *config.Config
		auditTrail *compliance.AuditTrail
		tempDir    string
	)

	BeforeEach(func() {
		var err error
		tempDir, err = os.MkdirTemp("", "audit-test-*")
		Expect(err).NotTo(HaveOccurred())

		cfg = &config.Config{
			Compliance: config.ComplianceConfig{
				AuditRetention:      "7d",
				AttestationRequired: true,
				ReportPath:          tempDir,
			},
		}
	})

	AfterEach(func() {
		if tempDir != "" {
			os.RemoveAll(tempDir)
		}
	})

	Describe("Creating a new audit trail", func() {
		Context("when configuration is valid", func() {
			It("should create an audit trail successfully", func() {
				var err error
				auditTrail, err = compliance.NewAuditTrail(cfg)
				Expect(err).NotTo(HaveOccurred())
				Expect(auditTrail).NotTo(BeNil())
			})

			It("should create the audit directory", func() {
				_, err := compliance.NewAuditTrail(cfg)
				Expect(err).NotTo(HaveOccurred())
				Expect(tempDir).To(BeADirectory())
			})
		})
	})

	Describe("Logging audit events", func() {
		BeforeEach(func() {
			var err error
			auditTrail, err = compliance.NewAuditTrail(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when logging a basic event", func() {
			It("should log the event successfully", func() {
				actor := compliance.Actor{
					Type: "user",
					ID:   "user123",
					Name: "Test User",
				}

				resource := compliance.Resource{
					Type:     "artifact",
					ID:       "artifact456",
					Name:     "test-artifact",
					Version:  "1.0.0",
					Location: "registry.example.com/test:1.0.0",
				}

				details := map[string]string{
					"build_id": "12345",
					"env":      "production",
				}

				err := auditTrail.LogEvent(
					compliance.EventTypeSigning,
					actor,
					resource,
					compliance.ActionSign,
					compliance.OutcomeSuccess,
					details,
				)
				Expect(err).NotTo(HaveOccurred())
			})
		})

		Context("when logging multiple events", func() {
			It("should maintain event order and integrity", func() {
				actor := compliance.Actor{
					Type: "system",
					ID:   "system001",
					Name: "MCStack System",
				}

				resource := compliance.Resource{
					Type: "bundle",
					ID:   "bundle789",
					Name: "webapp-bundle",
				}

				// Log first event
				err := auditTrail.LogEvent(
					compliance.EventTypeDeployment,
					actor,
					resource,
					compliance.ActionCreate,
					compliance.OutcomeSuccess,
					map[string]string{"step": "1"},
				)
				Expect(err).NotTo(HaveOccurred())

				// Log second event
				err = auditTrail.LogEvent(
					compliance.EventTypeDeployment,
					actor,
					resource,
					compliance.ActionExecute,
					compliance.OutcomeSuccess,
					map[string]string{"step": "2"},
				)
				Expect(err).NotTo(HaveOccurred())

				// Verify integrity
				err = auditTrail.VerifyIntegrity()
				Expect(err).NotTo(HaveOccurred())
			})
		})

		Context("when logging different event types", func() {
			It("should handle all event types correctly", func() {
				actor := compliance.Actor{
					Type: "service",
					ID:   "crypto-service",
					Name: "Crypto Service",
				}

				resource := compliance.Resource{
					Type: "key",
					ID:   "key123",
					Name: "encryption-key",
				}

				eventTypes := []string{
					compliance.EventTypeEncryption,
					compliance.EventTypeDecryption,
					compliance.EventTypeAccess,
					compliance.EventTypeConfiguration,
					compliance.EventTypeScan,
				}

				for _, eventType := range eventTypes {
					err := auditTrail.LogEvent(
						eventType,
						actor,
						resource,
						compliance.ActionRead,
						compliance.OutcomeSuccess,
						map[string]string{"test": "true"},
					)
					Expect(err).NotTo(HaveOccurred())
				}
			})
		})

		Context("when logging failed operations", func() {
			It("should record failure outcomes", func() {
				actor := compliance.Actor{
					Type: "user",
					ID:   "user456",
					Name: "Failed User",
				}

				resource := compliance.Resource{
					Type: "config",
					ID:   "config123",
					Name: "security-config",
				}

				err := auditTrail.LogEvent(
					compliance.EventTypeAccess,
					actor,
					resource,
					compliance.ActionRead,
					compliance.OutcomeFailure,
					map[string]string{
						"error":  "access denied",
						"reason": "insufficient permissions",
					},
				)
				Expect(err).NotTo(HaveOccurred())
			})
		})
	})

	Describe("Querying audit events", func() {
		BeforeEach(func() {
			var err error
			auditTrail, err = compliance.NewAuditTrail(cfg)
			Expect(err).NotTo(HaveOccurred())

			// Create test events
			actors := []compliance.Actor{
				{Type: "user", ID: "user1", Name: "User One"},
				{Type: "user", ID: "user2", Name: "User Two"},
				{Type: "system", ID: "sys1", Name: "System One"},
			}

			resources := []compliance.Resource{
				{Type: "artifact", ID: "art1", Name: "Artifact One"},
				{Type: "bundle", ID: "bun1", Name: "Bundle One"},
			}

			// Log multiple events for testing
			for i, actor := range actors {
				for j, resource := range resources {
					outcome := compliance.OutcomeSuccess
					if i == 1 && j == 1 { // Make one event fail
						outcome = compliance.OutcomeFailure
					}

					err := auditTrail.LogEvent(
						compliance.EventTypeDeployment,
						actor,
						resource,
						compliance.ActionCreate,
						outcome,
						map[string]string{"index": string(rune('A' + i + j))},
					)
					Expect(err).NotTo(HaveOccurred())
				}
			}
		})

		Context("when querying all events", func() {
			It("should return all events", func() {
				query := &compliance.AuditQuery{}
				events, err := auditTrail.QueryEvents(query)
				Expect(err).NotTo(HaveOccurred())
				Expect(events).To(HaveLen(6)) // 3 actors * 2 resources = 6 events
			})
		})

		Context("when querying by actor", func() {
			It("should return events for specific actor", func() {
				query := &compliance.AuditQuery{
					ActorIDs: []string{"user1"},
				}
				events, err := auditTrail.QueryEvents(query)
				Expect(err).NotTo(HaveOccurred())
				Expect(events).To(HaveLen(2)) // user1 * 2 resources = 2 events

				for _, event := range events {
					Expect(event.Actor.ID).To(Equal("user1"))
				}
			})
		})

		Context("when querying by resource type", func() {
			It("should return events for specific resource type", func() {
				query := &compliance.AuditQuery{
					ResourceTypes: []string{"artifact"},
				}
				events, err := auditTrail.QueryEvents(query)
				Expect(err).NotTo(HaveOccurred())
				Expect(events).To(HaveLen(3)) // 3 actors * 1 artifact = 3 events

				for _, event := range events {
					Expect(event.Resource.Type).To(Equal("artifact"))
				}
			})
		})

		Context("when querying by outcome", func() {
			It("should return events with specific outcome", func() {
				query := &compliance.AuditQuery{
					Outcomes: []string{compliance.OutcomeFailure},
				}
				events, err := auditTrail.QueryEvents(query)
				Expect(err).NotTo(HaveOccurred())
				Expect(events).To(HaveLen(1)) // Only one failure event

				for _, event := range events {
					Expect(event.Outcome).To(Equal(compliance.OutcomeFailure))
				}
			})
		})

		Context("when querying with time range", func() {
			It("should return events within time range", func() {
				now := time.Now()
				pastHour := now.Add(-time.Hour)
				futureHour := now.Add(time.Hour)

				query := &compliance.AuditQuery{
					StartTime: &pastHour,
					EndTime:   &futureHour,
				}
				events, err := auditTrail.QueryEvents(query)
				Expect(err).NotTo(HaveOccurred())
				Expect(events).To(HaveLen(6)) // All events should be within this range

				for _, event := range events {
					Expect(event.Timestamp).To(BeTemporally(">=", pastHour))
					Expect(event.Timestamp).To(BeTemporally("<=", futureHour))
				}
			})
		})

		Context("when querying with pagination", func() {
			It("should respect limit and offset", func() {
				query := &compliance.AuditQuery{
					Limit:  2,
					Offset: 1,
				}
				events, err := auditTrail.QueryEvents(query)
				Expect(err).NotTo(HaveOccurred())
				Expect(events).To(HaveLen(2))
			})
		})
	})

	Describe("Generating audit reports", func() {
		BeforeEach(func() {
			var err error
			auditTrail, err = compliance.NewAuditTrail(cfg)
			Expect(err).NotTo(HaveOccurred())

			// Create diverse test events for reporting
			actors := []compliance.Actor{
				{Type: "user", ID: "user1", Name: "User One"},
				{Type: "user", ID: "user2", Name: "User Two"},
				{Type: "system", ID: "sys1", Name: "System One"},
			}

			resources := []compliance.Resource{
				{Type: "artifact", ID: "art1", Name: "Artifact One"},
				{Type: "bundle", ID: "bun1", Name: "Bundle One"},
				{Type: "key", ID: "key1", Name: "Key One"},
			}

			eventTypes := []string{
				compliance.EventTypeSigning,
				compliance.EventTypeDeployment,
				compliance.EventTypeEncryption,
			}

			outcomes := []string{
				compliance.OutcomeSuccess,
				compliance.OutcomeSuccess,
				compliance.OutcomeFailure, // Add some failures
			}

			// Generate events
			for i, actor := range actors {
				for j, resource := range resources {
					err := auditTrail.LogEvent(
						eventTypes[j%len(eventTypes)],
						actor,
						resource,
						compliance.ActionCreate,
						outcomes[j%len(outcomes)],
						map[string]string{
							"test_run": "report_generation",
							"index":    string(rune('A' + i + j)),
						},
					)
					Expect(err).NotTo(HaveOccurred())
				}
			}
		})

		Context("when generating a report", func() {
			It("should create a comprehensive audit report", func() {
				startTime := time.Now().Add(-time.Hour)
				endTime := time.Now().Add(time.Hour)

				report, err := auditTrail.GenerateReport(startTime, endTime)
				Expect(err).NotTo(HaveOccurred())
				Expect(report).NotTo(BeNil())
				Expect(report.ID).NotTo(BeEmpty())
				Expect(report.GeneratedAt).To(BeTemporally("~", time.Now(), time.Minute))
				Expect(report.Period.StartTime).To(Equal(startTime))
				Expect(report.Period.EndTime).To(Equal(endTime))
				Expect(report.Summary.TotalEvents).To(Equal(9)) // 3 actors * 3 resources = 9 events
				Expect(report.Summary.SuccessfulEvents).To(Equal(6)) // 2/3 of events succeed
				Expect(report.Summary.FailedEvents).To(Equal(3)) // 1/3 of events fail
				Expect(report.Summary.UniqueActors).To(Equal(3))
				Expect(report.Summary.UniqueResources).To(Equal(3))
				Expect(report.Summary.ComplianceScore).To(BeNumerically("~", 66.67, 0.1))
				Expect(report.Summary.RiskLevel).To(Equal("medium"))
				Expect(report.EventsByType).To(HaveKey(compliance.EventTypeSigning))
				Expect(report.EventsByOutcome).To(HaveKey(compliance.OutcomeSuccess))
				Expect(report.EventsByOutcome).To(HaveKey(compliance.OutcomeFailure))
				Expect(report.TopActors).NotTo(BeEmpty())
				Expect(report.TopResources).NotTo(BeEmpty())
				Expect(report.Recommendations).NotTo(BeEmpty())
			})
		})

		Context("when generating report with high failure rate", func() {
			BeforeEach(func() {
				// Add many more failure events to trigger violation detection
				actor := compliance.Actor{Type: "user", ID: "failure_user", Name: "Failure User"}
				resource := compliance.Resource{Type: "test", ID: "test_resource", Name: "Test Resource"}

				for i := 0; i < 20; i++ {
					err := auditTrail.LogEvent(
						compliance.EventTypeAccess,
						actor,
						resource,
						compliance.ActionRead,
						compliance.OutcomeFailure,
						map[string]string{"failure_test": "true"},
					)
					Expect(err).NotTo(HaveOccurred())
				}
			})

			It("should detect compliance violations", func() {
				startTime := time.Now().Add(-time.Hour)
				endTime := time.Now().Add(time.Hour)

				report, err := auditTrail.GenerateReport(startTime, endTime)
				Expect(err).NotTo(HaveOccurred())
				Expect(report.Violations).NotTo(BeEmpty())

				// Should detect high failure rate violation
				foundViolation := false
				for _, violation := range report.Violations {
					if violation.ID == "high-failure-rate" {
						foundViolation = true
						Expect(violation.Severity).To(Equal("high"))
						Expect(violation.Events).NotTo(BeEmpty())
						break
					}
				}
				Expect(foundViolation).To(BeTrue())
			})
		})
	})

	Describe("Audit trail integrity", func() {
		BeforeEach(func() {
			var err error
			auditTrail, err = compliance.NewAuditTrail(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when verifying integrity of clean trail", func() {
			It("should pass integrity verification", func() {
				actor := compliance.Actor{Type: "user", ID: "integrity_user", Name: "Integrity User"}
				resource := compliance.Resource{Type: "test", ID: "integrity_resource", Name: "Integrity Resource"}

				// Log multiple events
				for i := 0; i < 5; i++ {
					err := auditTrail.LogEvent(
						compliance.EventTypeAccess,
						actor,
						resource,
						compliance.ActionRead,
						compliance.OutcomeSuccess,
						map[string]string{"sequence": string(rune('A' + i))},
					)
					Expect(err).NotTo(HaveOccurred())
				}

				err := auditTrail.VerifyIntegrity()
				Expect(err).NotTo(HaveOccurred())
			})
		})
	})

	Describe("Health checks", func() {
		Context("when audit trail is healthy", func() {
			BeforeEach(func() {
				var err error
				auditTrail, err = compliance.NewAuditTrail(cfg)
				Expect(err).NotTo(HaveOccurred())
			})

			It("should pass health check", func() {
				err := auditTrail.Health()
				Expect(err).NotTo(HaveOccurred())
			})
		})
	})
})

// Traditional unit tests using testify
type AuditTrailTestSuite struct {
	suite.Suite
	config     *config.Config
	auditTrail *compliance.AuditTrail
	tempDir    string
}

func (suite *AuditTrailTestSuite) SetupTest() {
	var err error
	suite.tempDir, err = os.MkdirTemp("", "audit-suite-test-*")
	suite.Require().NoError(err)

	suite.config = &config.Config{
		Compliance: config.ComplianceConfig{
			AuditRetention: "7d",
			ReportPath:     suite.tempDir,
		},
	}

	suite.auditTrail, err = compliance.NewAuditTrail(suite.config)
	suite.Require().NoError(err)
}

func (suite *AuditTrailTestSuite) TearDownTest() {
	if suite.tempDir != "" {
		os.RemoveAll(suite.tempDir)
	}
}

func (suite *AuditTrailTestSuite) TestLogEvent_Success() {
	actor := compliance.Actor{
		Type: "user",
		ID:   "test_user",
		Name: "Test User",
	}

	resource := compliance.Resource{
		Type: "artifact",
		ID:   "test_artifact",
		Name: "Test Artifact",
	}

	err := suite.auditTrail.LogEvent(
		compliance.EventTypeSigning,
		actor,
		resource,
		compliance.ActionSign,
		compliance.OutcomeSuccess,
		map[string]string{"test": "true"},
	)
	suite.NoError(err)
}

func (suite *AuditTrailTestSuite) TestQueryEvents_Success() {
	// Log a test event first
	actor := compliance.Actor{Type: "user", ID: "query_user", Name: "Query User"}
	resource := compliance.Resource{Type: "test", ID: "query_resource", Name: "Query Resource"}

	err := suite.auditTrail.LogEvent(
		compliance.EventTypeAccess,
		actor,
		resource,
		compliance.ActionRead,
		compliance.OutcomeSuccess,
		map[string]string{},
	)
	suite.Require().NoError(err)

	// Query events
	query := &compliance.AuditQuery{}
	events, err := suite.auditTrail.QueryEvents(query)
	suite.NoError(err)
	suite.Len(events, 2) // The test event + health check event from constructor
}

func (suite *AuditTrailTestSuite) TestGenerateReport_Success() {
	// Log some test events
	actor := compliance.Actor{Type: "user", ID: "report_user", Name: "Report User"}
	resource := compliance.Resource{Type: "test", ID: "report_resource", Name: "Report Resource"}

	err := suite.auditTrail.LogEvent(
		compliance.EventTypeDeployment,
		actor,
		resource,
		compliance.ActionCreate,
		compliance.OutcomeSuccess,
		map[string]string{},
	)
	suite.Require().NoError(err)

	startTime := time.Now().Add(-time.Hour)
	endTime := time.Now().Add(time.Hour)

	report, err := suite.auditTrail.GenerateReport(startTime, endTime)
	suite.NoError(err)
	suite.NotNil(report)
	suite.NotEmpty(report.ID)
	suite.True(report.Summary.TotalEvents > 0)
}

func (suite *AuditTrailTestSuite) TestVerifyIntegrity_Success() {
	err := suite.auditTrail.VerifyIntegrity()
	suite.NoError(err)
}

func (suite *AuditTrailTestSuite) TestHealth_Success() {
	err := suite.auditTrail.Health()
	suite.NoError(err)
}

func TestAuditTrailSuite(t *testing.T) {
	suite.Run(t, new(AuditTrailTestSuite))
}