// Package: compliance
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:compliance-impl:2025-01-27T10:00:00.123Z
// 
// Purpose: Software Bill of Materials (SBOM) generation and compliance tracking
// Governance: MCStack v13.5 compliance with SLSA 4+ and regulatory requirements
// Security: Supply chain attestation, vulnerability tracking, and license compliance
package compliance

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/sirupsen/logrus"
)

// SBOMGenerator generates Software Bill of Materials
type SBOMGenerator struct {
	config *config.Config
}

// SBOM represents a Software Bill of Materials
type SBOM struct {
	BOMFormat    string        `json:"bomFormat"`
	SpecVersion  string        `json:"specVersion"`
	SerialNumber string        `json:"serialNumber"`
	Version      int           `json:"version"`
	Metadata     SBOMMetadata  `json:"metadata"`
	Components   []Component   `json:"components"`
	Services     []Service     `json:"services,omitempty"`
	Dependencies []Dependency  `json:"dependencies,omitempty"`
	Compositions []Composition `json:"compositions,omitempty"`
	Annotations  []Annotation  `json:"annotations,omitempty"`
}

// SBOMMetadata contains metadata about the SBOM
type SBOMMetadata struct {
	Timestamp   time.Time    `json:"timestamp"`
	Tools       []Tool       `json:"tools"`
	Authors     []Author     `json:"authors,omitempty"`
	Component   Component    `json:"component"`
	Manufacture Tool         `json:"manufacture,omitempty"`
	Supplier    Organization `json:"supplier,omitempty"`
	Licenses    []License    `json:"licenses,omitempty"`
	Properties  []Property   `json:"properties,omitempty"`
}

// Component represents a software component
type Component struct {
	Type           string          `json:"type"`
	MimeType       string          `json:"mime-type,omitempty"`
	BOMRef         string          `json:"bom-ref"`
	Supplier       Organization    `json:"supplier,omitempty"`
	Author         string          `json:"author,omitempty"`
	Publisher      string          `json:"publisher,omitempty"`
	Group          string          `json:"group,omitempty"`
	Name           string          `json:"name"`
	Version        string          `json:"version,omitempty"`
	Description    string          `json:"description,omitempty"`
	Scope          string          `json:"scope,omitempty"`
	Hashes         []Hash          `json:"hashes,omitempty"`
	Licenses       []License       `json:"licenses,omitempty"`
	Copyright      string          `json:"copyright,omitempty"`
	CPE            string          `json:"cpe,omitempty"`
	PURL           string          `json:"purl,omitempty"`
	SWID           SWID            `json:"swid,omitempty"`
	PedigreeInfo   Pedigree        `json:"pedigree,omitempty"`
	ExternalRefs   []ExternalRef   `json:"externalReferences,omitempty"`
	Properties     []Property      `json:"properties,omitempty"`
	Components     []Component     `json:"components,omitempty"`
	Evidence       Evidence        `json:"evidence,omitempty"`
	Vulnerabilities []Vulnerability `json:"vulnerabilities,omitempty"`
}

// Service represents a service component
type Service struct {
	BOMRef         string           `json:"bom-ref"`
	Provider       Organization     `json:"provider,omitempty"`
	Group          string           `json:"group,omitempty"`
	Name           string           `json:"name"`
	Version        string           `json:"version,omitempty"`
	Description    string           `json:"description,omitempty"`
	Endpoints      []string         `json:"endpoints,omitempty"`
	Authenticated  bool             `json:"authenticated,omitempty"`
	XTrustBoundary bool             `json:"x-trust-boundary,omitempty"`
	Data           []ServiceData    `json:"data,omitempty"`
	Licenses       []License        `json:"licenses,omitempty"`
	ExternalRefs   []ExternalRef    `json:"externalReferences,omitempty"`
	Properties     []Property       `json:"properties,omitempty"`
	Services       []Service        `json:"services,omitempty"`
}

// Dependency represents component dependencies
type Dependency struct {
	Ref           string   `json:"ref"`
	DependsOn     []string `json:"dependsOn,omitempty"`
}

// Composition represents how components are assembled
type Composition struct {
	Aggregate   string   `json:"aggregate"`
	Assemblies  []string `json:"assemblies,omitempty"`
	Dependencies []string `json:"dependencies,omitempty"`
}

// Tool represents a tool used in the software development process
type Tool struct {
	Vendor     string     `json:"vendor,omitempty"`
	Name       string     `json:"name"`
	Version    string     `json:"version,omitempty"`
	Hashes     []Hash     `json:"hashes,omitempty"`
	ExternalRefs []ExternalRef `json:"externalReferences,omitempty"`
}

// Author represents an author of the SBOM
type Author struct {
	Name  string `json:"name"`
	Email string `json:"email,omitempty"`
	Phone string `json:"phone,omitempty"`
}

// Organization represents an organization
type Organization struct {
	Name    string    `json:"name"`
	URL     []string  `json:"url,omitempty"`
	Contact []Contact `json:"contact,omitempty"`
}

// Contact represents contact information
type Contact struct {
	Name  string `json:"name,omitempty"`
	Email string `json:"email,omitempty"`
	Phone string `json:"phone,omitempty"`
}

// Hash represents a cryptographic hash
type Hash struct {
	Algorithm string `json:"alg"`
	Content   string `json:"content"`
}

// License represents software license information
type License struct {
	ID          string    `json:"id,omitempty"`
	Name        string    `json:"name,omitempty"`
	Text        LicenseText `json:"text,omitempty"`
	URL         string    `json:"url,omitempty"`
	Licensing   Licensing `json:"licensing,omitempty"`
}

// LicenseText represents license text
type LicenseText struct {
	ContentType string `json:"contentType,omitempty"`
	Encoding    string `json:"encoding,omitempty"`
	Content     string `json:"content"`
}

// Licensing represents licensing information
type Licensing struct {
	AltIDs      []string `json:"altIds,omitempty"`
	Licensor    Organization `json:"licensor,omitempty"`
	Licensee    Organization `json:"licensee,omitempty"`
	Purchaser   Organization `json:"purchaser,omitempty"`
	PurchaseOrder string `json:"purchaseOrder,omitempty"`
	LicenseTypes []string `json:"licenseTypes,omitempty"`
	LastRenewal  time.Time `json:"lastRenewal,omitempty"`
	Expiration   time.Time `json:"expiration,omitempty"`
}

// SWID represents Software Identification tags
type SWID struct {
	TagID     string `json:"tagId"`
	Name      string `json:"name"`
	Version   string `json:"version,omitempty"`
	TagVersion int    `json:"tagVersion,omitempty"`
	Patch     bool   `json:"patch,omitempty"`
	Text      SWIDText `json:"text,omitempty"`
	URL       string `json:"url,omitempty"`
}

// SWIDText represents SWID text content
type SWIDText struct {
	ContentType string `json:"contentType,omitempty"`
	Encoding    string `json:"encoding,omitempty"`
	Content     string `json:"content"`
}

// Pedigree represents component ancestry information
type Pedigree struct {
	Ancestors   []Component `json:"ancestors,omitempty"`
	Descendants []Component `json:"descendants,omitempty"`
	Variants    []Component `json:"variants,omitempty"`
	Commits     []Commit    `json:"commits,omitempty"`
	Patches     []Patch     `json:"patches,omitempty"`
	Notes       string      `json:"notes,omitempty"`
}

// Commit represents a source code commit
type Commit struct {
	UID       string    `json:"uid,omitempty"`
	URL       string    `json:"url,omitempty"`
	Author    Author    `json:"author,omitempty"`
	Committer Author    `json:"committer,omitempty"`
	Message   string    `json:"message,omitempty"`
}

// Patch represents a software patch
type Patch struct {
	Type     string     `json:"type"`
	Diff     PatchDiff  `json:"diff,omitempty"`
	Resolves []Issue    `json:"resolves,omitempty"`
}

// PatchDiff represents patch diff information
type PatchDiff struct {
	Text PatchText `json:"text,omitempty"`
	URL  string    `json:"url,omitempty"`
}

// PatchText represents patch text content
type PatchText struct {
	ContentType string `json:"contentType,omitempty"`
	Encoding    string `json:"encoding,omitempty"`
	Content     string `json:"content"`
}

// Issue represents an issue or bug
type Issue struct {
	Type        string `json:"type"`
	ID          string `json:"id,omitempty"`
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
	Source      IssueSource `json:"source,omitempty"`
	References  []string `json:"references,omitempty"`
}

// IssueSource represents the source of an issue
type IssueSource struct {
	Name string `json:"name,omitempty"`
	URL  string `json:"url,omitempty"`
}

// ExternalRef represents external references
type ExternalRef struct {
	Type    string    `json:"type"`
	URL     string    `json:"url"`
	Comment string    `json:"comment,omitempty"`
	Hashes  []Hash    `json:"hashes,omitempty"`
}

// Property represents a name-value property
type Property struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

// Evidence represents evidence of a component
type Evidence struct {
	Licenses   []License   `json:"licenses,omitempty"`
	Copyright  []Copyright `json:"copyright,omitempty"`
}

// Copyright represents copyright information
type Copyright struct {
	Text string `json:"text"`
}

// Vulnerability represents a security vulnerability
type Vulnerability struct {
	BOMRef      string               `json:"bom-ref,omitempty"`
	ID          string               `json:"id"`
	Source      VulnerabilitySource  `json:"source,omitempty"`
	References  []VulnerabilityRef   `json:"references,omitempty"`
	Ratings     []VulnerabilityRating `json:"ratings,omitempty"`
	CWEs        []int                `json:"cwes,omitempty"`
	Description string               `json:"description,omitempty"`
	Detail      string               `json:"detail,omitempty"`
	Recommendation string            `json:"recommendation,omitempty"`
	Advisories  []Advisory           `json:"advisories,omitempty"`
	Created     time.Time            `json:"created,omitempty"`
	Published   time.Time            `json:"published,omitempty"`
	Updated     time.Time            `json:"updated,omitempty"`
	Credits     VulnerabilityCredits `json:"credits,omitempty"`
	Tools       []Tool               `json:"tools,omitempty"`
	Analysis    VulnerabilityAnalysis `json:"analysis,omitempty"`
	Affects     []VulnerabilityAffect `json:"affects,omitempty"`
	Properties  []Property           `json:"properties,omitempty"`
}

// VulnerabilitySource represents vulnerability source
type VulnerabilitySource struct {
	Name string `json:"name,omitempty"`
	URL  string `json:"url,omitempty"`
}

// VulnerabilityRef represents vulnerability reference
type VulnerabilityRef struct {
	ID     string               `json:"id"`
	Source VulnerabilitySource  `json:"source,omitempty"`
}

// VulnerabilityRating represents vulnerability rating
type VulnerabilityRating struct {
	Source        VulnerabilitySource `json:"source,omitempty"`
	Score         float64             `json:"score,omitempty"`
	Severity      string              `json:"severity,omitempty"`
	Method        string              `json:"method,omitempty"`
	Vector        string              `json:"vector,omitempty"`
	Justification string              `json:"justification,omitempty"`
}

// Advisory represents security advisory
type Advisory struct {
	Title string `json:"title,omitempty"`
	URL   string `json:"url"`
}

// VulnerabilityCredits represents vulnerability credits
type VulnerabilityCredits struct {
	Organizations []Organization `json:"organizations,omitempty"`
	Individuals   []Author       `json:"individuals,omitempty"`
}

// VulnerabilityAnalysis represents vulnerability analysis
type VulnerabilityAnalysis struct {
	State         string    `json:"state,omitempty"`
	Justification string    `json:"justification,omitempty"`
	Response      []string  `json:"response,omitempty"`
	Detail        string    `json:"detail,omitempty"`
	FirstIssued   time.Time `json:"firstIssued,omitempty"`
	LastUpdated   time.Time `json:"lastUpdated,omitempty"`
}

// VulnerabilityAffect represents what a vulnerability affects
type VulnerabilityAffect struct {
	Ref      string              `json:"ref"`
	Versions []VulnerabilityVersion `json:"versions,omitempty"`
}

// VulnerabilityVersion represents affected version
type VulnerabilityVersion struct {
	Version string `json:"version,omitempty"`
	Range   string `json:"range,omitempty"`
	Status  string `json:"status,omitempty"`
}

// ServiceData represents data processed by a service
type ServiceData struct {
	Flow         string             `json:"flow"`
	Classification string           `json:"classification,omitempty"`
	Governance   DataGovernance     `json:"governance,omitempty"`
}

// DataGovernance represents data governance information
type DataGovernance struct {
	Custodians []Organization `json:"custodians,omitempty"`
	Stewards   []Organization `json:"stewards,omitempty"`
	Owners     []Organization `json:"owners,omitempty"`
}

// Annotation represents an annotation
type Annotation struct {
	BOMRef      string    `json:"bom-ref,omitempty"`
	Subjects    []string  `json:"subjects"`
	Annotator   Author    `json:"annotator,omitempty"`
	Timestamp   time.Time `json:"timestamp"`
	Text        string    `json:"text"`
}

// ScanResult represents the result of a compliance scan
type ScanResult struct {
	SBOM           *SBOM              `json:"sbom"`
	Vulnerabilities []Vulnerability   `json:"vulnerabilities"`
	Licenses       []LicenseIssue     `json:"license_issues"`
	Compliance     ComplianceStatus   `json:"compliance"`
	Timestamp      time.Time          `json:"timestamp"`
}

// LicenseIssue represents a license compliance issue
type LicenseIssue struct {
	Component     string `json:"component"`
	License       string `json:"license"`
	Severity      string `json:"severity"`
	Description   string `json:"description"`
	Recommendation string `json:"recommendation"`
}

// ComplianceStatus represents overall compliance status
type ComplianceStatus struct {
	SLSA          SLSALevel          `json:"slsa"`
	Vulnerabilities VulnSummary      `json:"vulnerabilities"`
	Licenses      LicenseSummary     `json:"licenses"`
	Overall       string             `json:"overall"`
	Recommendations []string         `json:"recommendations"`
}

// SLSALevel represents SLSA compliance level
type SLSALevel struct {
	Level        int      `json:"level"`
	Requirements []string `json:"requirements"`
	Missing      []string `json:"missing"`
}

// VulnSummary represents vulnerability summary
type VulnSummary struct {
	Critical int `json:"critical"`
	High     int `json:"high"`
	Medium   int `json:"medium"`
	Low      int `json:"low"`
	Total    int `json:"total"`
}

// LicenseSummary represents license compliance summary
type LicenseSummary struct {
	Approved   int `json:"approved"`
	Restricted int `json:"restricted"`
	Unknown    int `json:"unknown"`
	Total      int `json:"total"`
}

// NewSBOMGenerator creates a new SBOM generator
func NewSBOMGenerator(cfg *config.Config) (*SBOMGenerator, error) {
	logrus.Info("Creating SBOM generator")

	generator := &SBOMGenerator{
		config: cfg,
	}

	logrus.Info("SBOM generator created successfully")
	return generator, nil
}

// GenerateSBOM generates an SBOM for the given components
func (sg *SBOMGenerator) GenerateSBOM(name, version string, components []Component) (*SBOM, error) {
	logrus.WithFields(logrus.Fields{
		"name":       name,
		"version":    version,
		"components": len(components),
	}).Info("Generating SBOM")

	// Generate serial number
	serialNumber := sg.generateSerialNumber()

	// Create main component
	mainComponent := Component{
		Type:        "application",
		BOMRef:      "pkg:generic/" + name + "@" + version,
		Name:        name,
		Version:     version,
		Description: "Main application component",
		Scope:       "required",
	}

	// Create SBOM
	sbom := &SBOM{
		BOMFormat:    "CycloneDX",
		SpecVersion:  "1.6",
		SerialNumber: serialNumber,
		Version:      1,
		Metadata: SBOMMetadata{
			Timestamp: time.Now(),
			Tools: []Tool{
				{
					Vendor:  "MCStack",
					Name:    "mc-poly-installer",
					Version: "1.0.0",
				},
			},
			Component: mainComponent,
		},
		Components: components,
	}

	// Generate dependencies
	sbom.Dependencies = sg.generateDependencies(mainComponent, components)

	// Sort components for consistency
	sort.Slice(sbom.Components, func(i, j int) bool {
		return sbom.Components[i].Name < sbom.Components[j].Name
	})

	logrus.WithField("serial", serialNumber).Info("SBOM generated successfully")
	return sbom, nil
}

// ScanDirectory scans a directory for components and generates an SBOM
func (sg *SBOMGenerator) ScanDirectory(dirPath string) (*SBOM, error) {
	logrus.WithField("path", dirPath).Info("Scanning directory for SBOM generation")

	components := []Component{}
	
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories
		if info.IsDir() {
			return nil
		}

		// Create component from file
		component := sg.createComponentFromFile(path, info)
		if component != nil {
			components = append(components, *component)
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to scan directory: %w", err)
	}

	// Extract project name from directory
	projectName := filepath.Base(dirPath)
	return sg.GenerateSBOM(projectName, "1.0.0", components)
}

// WriteSBOM writes an SBOM to a file
func (sg *SBOMGenerator) WriteSBOM(sbom *SBOM, outputPath string) error {
	logrus.WithField("output", outputPath).Info("Writing SBOM to file")

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	// Marshal SBOM to JSON
	data, err := json.MarshalIndent(sbom, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal SBOM: %w", err)
	}

	// Write to file
	if err := os.WriteFile(outputPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write SBOM file: %w", err)
	}

	logrus.WithField("output", outputPath).Info("SBOM written successfully")
	return nil
}

// ValidateSBOM validates an SBOM against CycloneDX schema
func (sg *SBOMGenerator) ValidateSBOM(sbom *SBOM) error {
	logrus.Info("Validating SBOM")

	// Basic validation checks
	if sbom.BOMFormat != "CycloneDX" {
		return fmt.Errorf("invalid BOM format: expected CycloneDX, got %s", sbom.BOMFormat)
	}

	if sbom.SpecVersion == "" {
		return fmt.Errorf("spec version is required")
	}

	if sbom.SerialNumber == "" {
		return fmt.Errorf("serial number is required")
	}

	if sbom.Metadata.Component.Name == "" {
		return fmt.Errorf("metadata component name is required")
	}

	// Validate components
	for i, component := range sbom.Components {
		if component.Name == "" {
			return fmt.Errorf("component %d: name is required", i)
		}
		if component.BOMRef == "" {
			return fmt.Errorf("component %d: bom-ref is required", i)
		}
	}

	logrus.Info("SBOM validation successful")
	return nil
}

// Helper methods

func (sg *SBOMGenerator) generateSerialNumber() string {
	// Generate a UUID-like serial number
	timestamp := time.Now().Unix()
	hash := sha256.Sum256([]byte(fmt.Sprintf("mcstack-sbom-%d", timestamp)))
	return fmt.Sprintf("urn:uuid:%x-%x-%x-%x-%x",
		hash[0:4], hash[4:6], hash[6:8], hash[8:10], hash[10:16])
}

func (sg *SBOMGenerator) generateDependencies(main Component, components []Component) []Dependency {
	// Simple dependency structure: main component depends on all others
	dependencies := []Dependency{
		{
			Ref: main.BOMRef,
			DependsOn: make([]string, 0, len(components)),
		},
	}

	for _, component := range components {
		dependencies[0].DependsOn = append(dependencies[0].DependsOn, component.BOMRef)
	}

	return dependencies
}

func (sg *SBOMGenerator) createComponentFromFile(path string, info os.FileInfo) *Component {
	// Determine component type based on file extension
	ext := strings.ToLower(filepath.Ext(path))
	var componentType string

	switch ext {
	case ".jar", ".war", ".ear":
		componentType = "library"
	case ".exe", ".dll", ".so", ".dylib":
		componentType = "library"
	case ".js", ".ts", ".py", ".go", ".java", ".cpp", ".c":
		componentType = "file"
	case ".json", ".xml", ".yaml", ".yml":
		if strings.Contains(strings.ToLower(filepath.Base(path)), "package") {
			componentType = "file"
		} else {
			return nil // Skip non-package files
		}
	default:
		return nil // Skip unknown file types
	}

	// Calculate file hash
	hash := sg.calculateFileHash(path)
	
	// Create component
	component := &Component{
		Type:        componentType,
		BOMRef:      fmt.Sprintf("pkg:generic/%s@%s", filepath.Base(path), "unknown"),
		Name:        filepath.Base(path),
		Version:     "unknown",
		Description: fmt.Sprintf("File component: %s", path),
		Scope:       "required",
		Hashes: []Hash{
			{
				Algorithm: "SHA-256",
				Content:   hash,
			},
		},
		Properties: []Property{
			{
				Name:  "file:path",
				Value: path,
			},
			{
				Name:  "file:size",
				Value: fmt.Sprintf("%d", info.Size()),
			},
			{
				Name:  "file:modified",
				Value: info.ModTime().Format(time.RFC3339),
			},
		},
	}

	return component
}

func (sg *SBOMGenerator) calculateFileHash(path string) string {
	data, err := os.ReadFile(path)
	if err != nil {
		logrus.WithError(err).WithField("path", path).Warn("Failed to read file for hashing")
		return ""
	}

	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:])
}

// Health checks the health of the SBOM generator
func (sg *SBOMGenerator) Health() error {
	logrus.Debug("Checking SBOM generator health")

	// Test SBOM generation with minimal data
	testComponents := []Component{
		{
			Type:    "library",
			BOMRef:  "pkg:generic/test-component@1.0.0",
			Name:    "test-component",
			Version: "1.0.0",
		},
	}

	sbom, err := sg.GenerateSBOM("health-check", "1.0.0", testComponents)
	if err != nil {
		return fmt.Errorf("SBOM generation health check failed: %w", err)
	}

	// Validate generated SBOM
	if err := sg.ValidateSBOM(sbom); err != nil {
		return fmt.Errorf("SBOM validation health check failed: %w", err)
	}

	return nil
}