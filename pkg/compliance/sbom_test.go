// Package: compliance_test
// Agent: AG-013 (Claude <PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:sbom-test:2025-01-27T10:00:00.123Z
// 
// Purpose: BDD tests for compliance SBOM generation
// Governance: MCStack v13.5 test compliance with comprehensive coverage
// Security: Test SBOM generation and validation processes
package compliance_test

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/suite"

	"github.com/mchorfa/mc-poly-installer/pkg/compliance"
	"github.com/mchorfa/mc-poly-installer/pkg/config"
)

func TestCompliance(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Compliance Package Suite")
}

var _ = Describe("SBOM Generator", func() {
	var (
		cfg       *config.Config
		generator *compliance.SBOMGenerator
		tempDir   string
	)

	BeforeEach(func() {
		var err error
		tempDir, err = os.MkdirTemp("", "sbom-test-*")
		Expect(err).NotTo(HaveOccurred())

		cfg = &config.Config{
			Compliance: config.ComplianceConfig{
				AuditRetention:      "30d",
				SBOMFormat:          []string{"cyclonedx"},
				AttestationRequired: true,
				ReportPath:          tempDir,
			},
		}
	})

	AfterEach(func() {
		if tempDir != "" {
			os.RemoveAll(tempDir)
		}
	})

	Describe("Creating a new SBOM generator", func() {
		Context("when configuration is valid", func() {
			It("should create a generator successfully", func() {
				var err error
				generator, err = compliance.NewSBOMGenerator(cfg)
				Expect(err).NotTo(HaveOccurred())
				Expect(generator).NotTo(BeNil())
			})
		})
	})

	Describe("SBOM generation", func() {
		BeforeEach(func() {
			var err error
			generator, err = compliance.NewSBOMGenerator(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when generating SBOM with components", func() {
			It("should generate a valid SBOM", func() {
				components := []compliance.Component{
					{
						Type:        "library",
						BOMRef:      "pkg:generic/test-lib@1.0.0",
						Name:        "test-lib",
						Version:     "1.0.0",
						Description: "Test library component",
						Scope:       "required",
						Hashes: []compliance.Hash{
							{
								Algorithm: "SHA-256",
								Content:   "abc123def456",
							},
						},
					},
					{
						Type:        "application",
						BOMRef:      "pkg:generic/test-app@2.0.0",
						Name:        "test-app",
						Version:     "2.0.0",
						Description: "Test application component",
						Scope:       "required",
					},
				}

				sbom, err := generator.GenerateSBOM("test-project", "1.0.0", components)
				Expect(err).NotTo(HaveOccurred())
				Expect(sbom).NotTo(BeNil())
				Expect(sbom.BOMFormat).To(Equal("CycloneDX"))
				Expect(sbom.SpecVersion).To(Equal("1.6"))
				Expect(sbom.SerialNumber).To(MatchRegexp("urn:uuid:.*"))
				Expect(sbom.Version).To(Equal(1))
				Expect(sbom.Metadata.Component.Name).To(Equal("test-project"))
				Expect(sbom.Metadata.Component.Version).To(Equal("1.0.0"))
				Expect(sbom.Components).To(HaveLen(2))
				Expect(sbom.Dependencies).NotTo(BeEmpty())
				Expect(sbom.Metadata.Timestamp).To(BeTemporally("~", time.Now(), time.Minute))
			})
		})

		Context("when generating SBOM with empty components", func() {
			It("should generate SBOM with only main component", func() {
				var components []compliance.Component

				sbom, err := generator.GenerateSBOM("empty-project", "0.1.0", components)
				Expect(err).NotTo(HaveOccurred())
				Expect(sbom.Components).To(BeEmpty())
				Expect(sbom.Metadata.Component.Name).To(Equal("empty-project"))
			})
		})

		Context("when generating SBOM with vulnerabilities", func() {
			It("should include vulnerability information", func() {
				components := []compliance.Component{
					{
						Type:    "library",
						BOMRef:  "pkg:generic/vulnerable-lib@1.0.0",
						Name:    "vulnerable-lib",
						Version: "1.0.0",
						Vulnerabilities: []compliance.Vulnerability{
							{
								ID:          "CVE-2023-12345",
								Description: "Test vulnerability",
								Ratings: []compliance.VulnerabilityRating{
									{
										Score:    7.5,
										Severity: "high",
										Method:   "CVSSv3",
									},
								},
							},
						},
					},
				}

				sbom, err := generator.GenerateSBOM("vuln-test", "1.0.0", components)
				Expect(err).NotTo(HaveOccurred())
				Expect(sbom.Components[0].Vulnerabilities).To(HaveLen(1))
				Expect(sbom.Components[0].Vulnerabilities[0].ID).To(Equal("CVE-2023-12345"))
			})
		})

		Context("when generating SBOM with licenses", func() {
			It("should include license information", func() {
				components := []compliance.Component{
					{
						Type:    "library",
						BOMRef:  "pkg:generic/licensed-lib@1.0.0",
						Name:    "licensed-lib",
						Version: "1.0.0",
						Licenses: []compliance.License{
							{
								ID:   "MIT",
								Name: "MIT License",
								URL:  "https://opensource.org/licenses/MIT",
							},
						},
					},
				}

				sbom, err := generator.GenerateSBOM("license-test", "1.0.0", components)
				Expect(err).NotTo(HaveOccurred())
				Expect(sbom.Components[0].Licenses).To(HaveLen(1))
				Expect(sbom.Components[0].Licenses[0].ID).To(Equal("MIT"))
			})
		})
	})

	Describe("Directory scanning", func() {
		BeforeEach(func() {
			var err error
			generator, err = compliance.NewSBOMGenerator(cfg)
			Expect(err).NotTo(HaveOccurred())

			// Create test files
			testFiles := map[string]string{
				"package.json":   `{"name": "test-package", "version": "1.0.0"}`,
				"main.go":        "package main\n\nfunc main() {}",
				"requirements.txt": "requests==2.25.1\nnumpy==1.21.0",
				"pom.xml":        `<?xml version="1.0"?><project></project>`,
			}

			for filename, content := range testFiles {
				filePath := filepath.Join(tempDir, filename)
				err := os.WriteFile(filePath, []byte(content), 0644)
				Expect(err).NotTo(HaveOccurred())
			}
		})

		Context("when scanning a directory", func() {
			It("should generate SBOM from directory contents", func() {
				sbom, err := generator.ScanDirectory(tempDir)
				Expect(err).NotTo(HaveOccurred())
				Expect(sbom).NotTo(BeNil())
				Expect(sbom.Components).NotTo(BeEmpty())

				// Should find package files
				componentNames := make([]string, len(sbom.Components))
				for i, comp := range sbom.Components {
					componentNames[i] = comp.Name
				}
				Expect(componentNames).To(ContainElements("package.json", "main.go", "pom.xml"))
			})
		})

		Context("when scanning non-existent directory", func() {
			It("should return an error", func() {
				_, err := generator.ScanDirectory("/non/existent/path")
				Expect(err).To(HaveOccurred())
			})
		})
	})

	Describe("SBOM validation", func() {
		BeforeEach(func() {
			var err error
			generator, err = compliance.NewSBOMGenerator(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when validating a valid SBOM", func() {
			It("should pass validation", func() {
				components := []compliance.Component{
					{
						Type:    "library",
						BOMRef:  "pkg:generic/valid-lib@1.0.0",
						Name:    "valid-lib",
						Version: "1.0.0",
					},
				}

				sbom, err := generator.GenerateSBOM("valid-project", "1.0.0", components)
				Expect(err).NotTo(HaveOccurred())

				err = generator.ValidateSBOM(sbom)
				Expect(err).NotTo(HaveOccurred())
			})
		})

		Context("when validating SBOM with invalid format", func() {
			It("should return validation error", func() {
				sbom := &compliance.SBOM{
					BOMFormat:    "InvalidFormat",
					SpecVersion:  "1.6",
					SerialNumber: "urn:uuid:test",
					Metadata: compliance.SBOMMetadata{
						Component: compliance.Component{
							Name: "test",
						},
					},
				}

				err := generator.ValidateSBOM(sbom)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("invalid BOM format"))
			})
		})

		Context("when validating SBOM with missing fields", func() {
			It("should return validation error for missing spec version", func() {
				sbom := &compliance.SBOM{
					BOMFormat:   "CycloneDX",
					SpecVersion: "",
				}

				err := generator.ValidateSBOM(sbom)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("spec version is required"))
			})

			It("should return validation error for missing serial number", func() {
				sbom := &compliance.SBOM{
					BOMFormat:    "CycloneDX",
					SpecVersion:  "1.6",
					SerialNumber: "",
				}

				err := generator.ValidateSBOM(sbom)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("serial number is required"))
			})

			It("should return validation error for missing component name", func() {
				sbom := &compliance.SBOM{
					BOMFormat:    "CycloneDX",
					SpecVersion:  "1.6",
					SerialNumber: "urn:uuid:test",
					Metadata: compliance.SBOMMetadata{
						Component: compliance.Component{
							Name: "",
						},
					},
				}

				err := generator.ValidateSBOM(sbom)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("metadata component name is required"))
			})
		})

		Context("when validating components", func() {
			It("should return validation error for component without name", func() {
				sbom := &compliance.SBOM{
					BOMFormat:    "CycloneDX",
					SpecVersion:  "1.6",
					SerialNumber: "urn:uuid:test",
					Metadata: compliance.SBOMMetadata{
						Component: compliance.Component{
							Name: "test",
						},
					},
					Components: []compliance.Component{
						{
							Name:   "", // Missing name
							BOMRef: "test-ref",
						},
					},
				}

				err := generator.ValidateSBOM(sbom)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("component 0: name is required"))
			})

			It("should return validation error for component without bom-ref", func() {
				sbom := &compliance.SBOM{
					BOMFormat:    "CycloneDX",
					SpecVersion:  "1.6",
					SerialNumber: "urn:uuid:test",
					Metadata: compliance.SBOMMetadata{
						Component: compliance.Component{
							Name: "test",
						},
					},
					Components: []compliance.Component{
						{
							Name:   "test-component",
							BOMRef: "", // Missing BOMRef
						},
					},
				}

				err := generator.ValidateSBOM(sbom)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("component 0: bom-ref is required"))
			})
		})
	})

	Describe("SBOM persistence", func() {
		BeforeEach(func() {
			var err error
			generator, err = compliance.NewSBOMGenerator(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when writing SBOM to file", func() {
			It("should create the output file", func() {
				components := []compliance.Component{
					{
						Type:    "library",
						BOMRef:  "pkg:generic/persist-lib@1.0.0",
						Name:    "persist-lib",
						Version: "1.0.0",
					},
				}

				sbom, err := generator.GenerateSBOM("persist-test", "1.0.0", components)
				Expect(err).NotTo(HaveOccurred())

				outputPath := filepath.Join(tempDir, "test-sbom.json")
				err = generator.WriteSBOM(sbom, outputPath)
				Expect(err).NotTo(HaveOccurred())

				Expect(outputPath).To(BeAnExistingFile())

				// Verify file content
				content, err := os.ReadFile(outputPath)
				Expect(err).NotTo(HaveOccurred())
				Expect(string(content)).To(ContainSubstring("CycloneDX"))
				Expect(string(content)).To(ContainSubstring("persist-test"))
			})
		})

		Context("when writing SBOM to nested directory", func() {
			It("should create the directory structure", func() {
				components := []compliance.Component{
					{
						Type:    "library",
						BOMRef:  "pkg:generic/nested-lib@1.0.0",
						Name:    "nested-lib",
						Version: "1.0.0",
					},
				}

				sbom, err := generator.GenerateSBOM("nested-test", "1.0.0", components)
				Expect(err).NotTo(HaveOccurred())

				outputPath := filepath.Join(tempDir, "reports", "nested", "sbom.json")
				err = generator.WriteSBOM(sbom, outputPath)
				Expect(err).NotTo(HaveOccurred())

				Expect(outputPath).To(BeAnExistingFile())
			})
		})
	})

	Describe("Health checks", func() {
		Context("when generator is healthy", func() {
			BeforeEach(func() {
				var err error
				generator, err = compliance.NewSBOMGenerator(cfg)
				Expect(err).NotTo(HaveOccurred())
			})

			It("should pass health check", func() {
				err := generator.Health()
				Expect(err).NotTo(HaveOccurred())
			})
		})
	})
})

// Traditional unit tests using testify
type SBOMTestSuite struct {
	suite.Suite
	config    *config.Config
	generator *compliance.SBOMGenerator
	tempDir   string
}

func (suite *SBOMTestSuite) SetupTest() {
	var err error
	suite.tempDir, err = os.MkdirTemp("", "sbom-suite-test-*")
	suite.Require().NoError(err)

	suite.config = &config.Config{
		Compliance: config.ComplianceConfig{
			SBOMFormat: []string{"cyclonedx"},
			ReportPath: suite.tempDir,
		},
	}

	suite.generator, err = compliance.NewSBOMGenerator(suite.config)
	suite.Require().NoError(err)
}

func (suite *SBOMTestSuite) TearDownTest() {
	if suite.tempDir != "" {
		os.RemoveAll(suite.tempDir)
	}
}

func (suite *SBOMTestSuite) TestGenerateSBOM_Success() {
	components := []compliance.Component{
		{
			Type:    "library",
			BOMRef:  "pkg:generic/test@1.0.0",
			Name:    "test",
			Version: "1.0.0",
		},
	}

	sbom, err := suite.generator.GenerateSBOM("test-project", "1.0.0", components)
	suite.NoError(err)
	suite.NotNil(sbom)
	suite.Equal("CycloneDX", sbom.BOMFormat)
	suite.Equal("test-project", sbom.Metadata.Component.Name)
	suite.Len(sbom.Components, 1)
}

func (suite *SBOMTestSuite) TestValidateSBOM_Success() {
	components := []compliance.Component{
		{
			Type:    "library",
			BOMRef:  "pkg:generic/valid@1.0.0",
			Name:    "valid",
			Version: "1.0.0",
		},
	}

	sbom, err := suite.generator.GenerateSBOM("valid-project", "1.0.0", components)
	suite.Require().NoError(err)

	err = suite.generator.ValidateSBOM(sbom)
	suite.NoError(err)
}

func (suite *SBOMTestSuite) TestHealth_Success() {
	err := suite.generator.Health()
	suite.NoError(err)
}

func TestSBOMSuite(t *testing.T) {
	suite.Run(t, new(SBOMTestSuite))
}