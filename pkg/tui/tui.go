package tui

import (
	"fmt"
	"os"

	"github.com/charmbracelet/bubbles/list"
	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/mchorfa/mc-poly-installer/pkg/config"
)

// Styles for the TUI
var (
	titleStyle = lipgloss.NewStyle().
			MarginLeft(2).
			Foreground(lipgloss.Color("#FAFAFA")).
			Background(lipgloss.Color("#7D56F4")).
			Padding(0, 1)

	statusMessageStyle = lipgloss.NewStyle().
				Foreground(lipgloss.AdaptiveColor{Light: "#04B575", Dark: "#04B575"}).
				Render

	docStyle = lipgloss.NewStyle().Margin(1, 2)

	focusedStyle = lipgloss.NewStyle().Foreground(lipgloss.Color("205"))
	blurredStyle = lipgloss.NewStyle().Foreground(lipgloss.Color("240"))
	cursorStyle  = focusedStyle.Copy()
	noStyle      = lipgloss.NewStyle()

	helpStyle = blurredStyle.Copy()
)

// ViewMode represents the current view mode
type ViewMode int

const (
	MainMenuView ViewMode = iota
	PipelineView
	BundleView
	ArtifactView
	TransmissionView
	ConfigView
	LogsView
)

// Model represents the TUI model
type Model struct {
	config      *config.Config
	currentView ViewMode
	mainMenu    list.Model
	textInput   textinput.Model
	statusMsg   string
	width       int
	height      int
	quitting    bool
}

// MainMenuItem represents a main menu item
type MainMenuItem struct {
	title, desc string
	action      ViewMode
}

func (i MainMenuItem) Title() string       { return i.title }
func (i MainMenuItem) Description() string { return i.desc }
func (i MainMenuItem) FilterValue() string { return i.title }

// Init initializes the TUI model
func (m Model) Init() tea.Cmd {
	return textinput.Blink
}

// Update handles messages and updates the model
func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.mainMenu.SetWidth(msg.Width)
		m.mainMenu.SetHeight(msg.Height - 10)
		return m, nil

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			if m.currentView == MainMenuView {
				m.quitting = true
				return m, tea.Quit
			} else {
				m.currentView = MainMenuView
				return m, nil
			}
		case "enter":
			if m.currentView == MainMenuView {
				if selectedItem, ok := m.mainMenu.SelectedItem().(MainMenuItem); ok {
					m.currentView = selectedItem.action
					m.statusMsg = fmt.Sprintf("Switched to %s", selectedItem.title)
				}
			}
		}
	}

	// Update the appropriate component based on current view
	switch m.currentView {
	case MainMenuView:
		m.mainMenu, cmd = m.mainMenu.Update(msg)
	default:
		m.textInput, cmd = m.textInput.Update(msg)
	}

	return m, cmd
}

// View renders the TUI
func (m Model) View() string {
	if m.quitting {
		return "Thanks for using MC Poly Installer!\n"
	}

	var s string

	// Header
	s += titleStyle.Render("MC Poly Installer - Universal Airgapped Pipeline Manager")
	s += "\n\n"

	// Content based on current view
	switch m.currentView {
	case MainMenuView:
		s += m.renderMainMenu()
	case PipelineView:
		s += m.renderPipelineView()
	case BundleView:
		s += m.renderBundleView()
	case ArtifactView:
		s += m.renderArtifactView()
	case TransmissionView:
		s += m.renderTransmissionView()
	case ConfigView:
		s += m.renderConfigView()
	case LogsView:
		s += m.renderLogsView()
	}

	// Status bar
	if m.statusMsg != "" {
		s += "\n" + statusMessageStyle(m.statusMsg)
	}

	// Help
	s += "\n\n" + helpStyle.Render("• q/ctrl+c: quit • enter: select • esc: back to main menu")

	return docStyle.Render(s)
}

func (m Model) renderMainMenu() string {
	return m.mainMenu.View()
}

func (m Model) renderPipelineView() string {
	return `Pipeline Management

Available Actions:
• Create Pipeline    - Create a new deployment pipeline
• Deploy Pipeline    - Deploy existing pipeline configuration
• Validate Pipeline  - Validate pipeline configuration
• Pipeline Status    - Check pipeline execution status
• Pipeline Logs      - View pipeline execution logs

Press 'esc' to return to main menu.`
}

func (m Model) renderBundleView() string {
	return `Bundle Management (Porter CNAB)

Available Actions:
• Create Bundle      - Create new CNAB bundle
• Publish Bundle     - Publish bundle to OCI registry
• Transfer Bundle    - Transfer bundle through transmission station
• Install Bundle     - Install bundle in airgapped environment
• Bundle Status      - Check bundle installation status

Press 'esc' to return to main menu.`
}

func (m Model) renderArtifactView() string {
	return `Artifact Management (JFrog)

Available Actions:
• Upload Artifacts   - Upload artifacts to repository
• Download Artifacts - Download artifacts from repository
• Scan Artifacts     - Security scan artifacts with Xray
• Distribute         - Distribute artifacts to target environments
• Artifact Status    - Check artifact status and metadata

Press 'esc' to return to main menu.`
}

func (m Model) renderTransmissionView() string {
	return `Transmission Station Management

Available Actions:
• Setup Station      - Configure transmission station
• Validate Transfer  - Validate transfer security and integrity
• Monitor Station    - Monitor station operations and health
• Audit Logs         - Review transmission audit logs
• Station Status     - Check station connectivity and status

Press 'esc' to return to main menu.`
}

func (m Model) renderConfigView() string {
	return `Configuration Management

Current Configuration:
• Online Environment: ` + m.config.Environments.Online.GitLab.URL + `
• Transmission: ` + m.config.Environments.Transmission.Endpoint + `
• Security: Signing ` + fmt.Sprintf("%t", m.config.Security.Signing.Enabled) + `
• Dagger Version: ` + m.config.Dagger.EngineVersion + `
• Porter Driver: ` + m.config.Porter.DefaultDriver + `

Available Actions:
• Edit Configuration
• Validate Configuration
• Export Configuration
• Reset to Defaults

Press 'esc' to return to main menu.`
}

func (m Model) renderLogsView() string {
	return `System Logs

Recent Activity:
[2025-07-17 14:30:15] INFO: TUI interface started
[2025-07-17 14:30:10] INFO: Configuration loaded from ~/.mc-poly/config.yaml
[2025-07-17 14:29:55] INFO: Dagger engine connection established
[2025-07-17 14:29:50] INFO: JFrog Artifactory connection verified
[2025-07-17 14:29:45] INFO: Porter CNAB driver initialized

Log Levels: ALL | ERROR | WARN | INFO | DEBUG

Press 'esc' to return to main menu.`
}

// Run starts the TUI application
func Run(cfg *config.Config) error {
	// Initialize main menu items
	items := []list.Item{
		MainMenuItem{title: "Pipeline Management", desc: "Manage CI/CD pipelines with Dagger", action: PipelineView},
		MainMenuItem{title: "Bundle Management", desc: "Manage CNAB bundles with Porter", action: BundleView},
		MainMenuItem{title: "Artifact Management", desc: "Manage artifacts with JFrog CLI", action: ArtifactView},
		MainMenuItem{title: "Transmission Station", desc: "Manage secure transfer operations", action: TransmissionView},
		MainMenuItem{title: "Configuration", desc: "View and edit application configuration", action: ConfigView},
		MainMenuItem{title: "System Logs", desc: "View application and operation logs", action: LogsView},
	}

	// Create list model
	mainMenu := list.New(items, list.NewDefaultDelegate(), 0, 0)
	mainMenu.Title = "Main Menu"
	mainMenu.SetShowStatusBar(false)
	mainMenu.SetFilteringEnabled(false)

	// Create text input model
	ti := textinput.New()
	ti.Placeholder = "Enter command..."
	ti.Focus()
	ti.CharLimit = 156
	ti.Width = 50

	// Create initial model
	m := Model{
		config:      cfg,
		currentView: MainMenuView,
		mainMenu:    mainMenu,
		textInput:   ti,
		statusMsg:   "Welcome to MC Poly Installer!",
	}

	// Start the program
	p := tea.NewProgram(m, tea.WithAltScreen(), tea.WithMouseCellMotion())
	if _, err := p.Run(); err != nil {
		fmt.Printf("Error running TUI: %v", err)
		os.Exit(1)
	}

	return nil
}
