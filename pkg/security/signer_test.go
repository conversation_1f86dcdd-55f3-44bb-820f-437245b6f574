// Package: security_test
// Agent: AG-013 (Claude <PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:security-test:2025-01-27T10:00:00.123Z
// 
// Purpose: BDD tests for security package with Sigstore/Cosign integration
// Governance: MCStack v13.5 test compliance with comprehensive coverage
// Security: Test security operations without compromising real keys
package security_test

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/suite"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/mchorfa/mc-poly-installer/pkg/security"
)

func TestSecurity(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Security Package Suite")
}

var _ = Describe("Security Signer", func() {
	var (
		cfg      *config.Config
		signer   *security.Signer
		ctx      context.Context
		tempDir  string
		keyPath  string
		certPath string
	)

	BeforeEach(func() {
		var err error
		tempDir, err = os.MkdirTemp("", "security-test-*")
		Expect(err).NotTo(HaveOccurred())

		keyPath = filepath.Join(tempDir, "test.key")
		certPath = filepath.Join(tempDir, "test.crt")

		cfg = &config.Config{
			Security: config.SecurityConfig{
				Signing: config.SigningConfig{
					Enabled:  true,
					Method:   "key",
					KeyPath:  keyPath,
					CertPath: certPath,
				},
				Verification: config.VerificationConfig{
					Required:  true,
					Policies:  []string{"slsa4", "sbom"},
					TrustRoot: "/tmp/trust-root",
				},
			},
		}

		ctx = context.Background()
	})

	AfterEach(func() {
		if tempDir != "" {
			os.RemoveAll(tempDir)
		}
	})

	Describe("Creating a new signer", func() {
		Context("when signing is enabled", func() {
			It("should create a signer successfully", func() {
				var err error
				signer, err = security.NewSigner(cfg)
				Expect(err).NotTo(HaveOccurred())
				Expect(signer).NotTo(BeNil())
			})
		})

		Context("when signing is disabled", func() {
			BeforeEach(func() {
				cfg.Security.Signing.Enabled = false
			})

			It("should return an error", func() {
				_, err := security.NewSigner(cfg)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("signing is disabled"))
			})
		})
	})

	Describe("Key pair generation", func() {
		BeforeEach(func() {
			var err error
			signer, err = security.NewSigner(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when generating a new key pair", func() {
			It("should create both private and public keys", func() {
				privPath, pubPath, err := signer.GenerateKeyPair(ctx, tempDir)
				Expect(err).NotTo(HaveOccurred())
				Expect(privPath).To(ContainSubstring("cosign.key"))
				Expect(pubPath).To(ContainSubstring("cosign.pub"))

				// Verify files exist
				Expect(privPath).To(BeAnExistingFile())
				Expect(pubPath).To(BeAnExistingFile())

				// Verify file permissions
				privInfo, err := os.Stat(privPath)
				Expect(err).NotTo(HaveOccurred())
				Expect(privInfo.Mode().Perm()).To(Equal(os.FileMode(0600)))

				pubInfo, err := os.Stat(pubPath)
				Expect(err).NotTo(HaveOccurred())
				Expect(pubInfo.Mode().Perm()).To(Equal(os.FileMode(0644)))
			})
		})

		Context("when output directory doesn't exist", func() {
			It("should create the directory and generate keys", func() {
				nonExistentDir := filepath.Join(tempDir, "nested", "dir")
				privPath, pubPath, err := signer.GenerateKeyPair(ctx, nonExistentDir)
				Expect(err).NotTo(HaveOccurred())
				Expect(privPath).To(BeAnExistingFile())
				Expect(pubPath).To(BeAnExistingFile())
			})
		})
	})

	Describe("Artifact signing", func() {
		BeforeEach(func() {
			var err error
			signer, err = security.NewSigner(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when signing with key method", func() {
			It("should sign an artifact successfully", func() {
				artifactRef := "registry.example.com/test-image:latest"
				opts := &security.SigningOptions{
					Annotations: map[string]string{
						"build-id": "12345",
						"env":      "test",
					},
				}

				result, err := signer.SignArtifact(ctx, artifactRef, opts)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).NotTo(BeNil())
				Expect(result.Signature).NotTo(BeEmpty())
				Expect(result.Metadata["method"]).To(Equal("key"))
				Expect(result.Metadata["artifact"]).To(Equal(artifactRef))
				Expect(result.Metadata["build-id"]).To(Equal("12345"))
				Expect(result.Metadata["env"]).To(Equal("test"))
				Expect(result.Timestamp).To(BeTemporally("~", time.Now(), time.Minute))
			})
		})

		Context("when signing with keyless method", func() {
			BeforeEach(func() {
				cfg.Security.Signing.Method = "keyless"
			})

			It("should sign an artifact using keyless method", func() {
				artifactRef := "registry.example.com/test-image:latest"
				opts := &security.SigningOptions{}

				result, err := signer.SignArtifact(ctx, artifactRef, opts)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).NotTo(BeNil())
				Expect(result.Signature).NotTo(BeEmpty())
				Expect(result.Metadata["method"]).To(Equal("keyless"))
				Expect(result.Metadata["fulcio_url"]).To(ContainSubstring("fulcio.sigstore.dev"))
				Expect(result.Metadata["rekor_url"]).To(ContainSubstring("rekor.sigstore.dev"))
			})
		})

		Context("when signing with unsupported method", func() {
			BeforeEach(func() {
				cfg.Security.Signing.Method = "unknown"
			})

			It("should return an error", func() {
				artifactRef := "registry.example.com/test-image:latest"
				_, err := signer.SignArtifact(ctx, artifactRef, nil)
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(ContainSubstring("unsupported signing method"))
			})
		})
	})

	Describe("Container image signing", func() {
		BeforeEach(func() {
			var err error
			signer, err = security.NewSigner(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when signing a container image", func() {
			It("should add container-specific metadata", func() {
				imageRef := "registry.example.com/myapp:v1.0.0"
				opts := &security.SigningOptions{}

				result, err := signer.SignContainerImage(ctx, imageRef, opts)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).NotTo(BeNil())
				Expect(result.Metadata["type"]).To(Equal("container_image"))
				Expect(result.Metadata["registry"]).To(Equal("registry.example.com"))
			})
		})

		Context("when signing an image with localhost registry", func() {
			It("should handle localhost registry correctly", func() {
				imageRef := "localhost:5000/myapp:latest"
				opts := &security.SigningOptions{}

				result, err := signer.SignContainerImage(ctx, imageRef, opts)
				Expect(err).NotTo(HaveOccurred())
				Expect(result.Metadata["registry"]).To(Equal("localhost:5000"))
			})
		})
	})

	Describe("Artifact verification", func() {
		BeforeEach(func() {
			var err error
			signer, err = security.NewSigner(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when verifying a valid artifact", func() {
			It("should return a successful verification result", func() {
				artifactRef := "registry.example.com/test-image:latest"
				opts := &security.VerificationOptions{
					CertIdentity:   "<EMAIL>",
					CertOIDCIssuer: "https://github.com/login/oauth",
				}

				result, err := signer.VerifyArtifact(ctx, artifactRef, opts)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).NotTo(BeNil())
				Expect(result.Valid).To(BeTrue())
				Expect(result.Signer).NotTo(BeEmpty())
				Expect(result.Metadata["cert_identity"]).To(Equal("<EMAIL>"))
				Expect(result.Metadata["cert_oidc_issuer"]).To(Equal("https://github.com/login/oauth"))
				Expect(result.Errors).To(BeEmpty())
			})
		})

		Context("when verifying with empty artifact reference", func() {
			It("should return an invalid verification result", func() {
				artifactRef := ""
				opts := &security.VerificationOptions{}

				result, err := signer.VerifyArtifact(ctx, artifactRef, opts)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).NotTo(BeNil())
				Expect(result.Valid).To(BeFalse())
				Expect(result.Errors).To(ContainElement("empty artifact reference"))
			})
		})
	})

	Describe("Container image verification", func() {
		BeforeEach(func() {
			var err error
			signer, err = security.NewSigner(cfg)
			Expect(err).NotTo(HaveOccurred())
		})

		Context("when verifying a container image", func() {
			It("should add container-specific metadata", func() {
				imageRef := "registry.example.com/myapp:v1.0.0"
				opts := &security.VerificationOptions{}

				result, err := signer.VerifyContainerImage(ctx, imageRef, opts)
				Expect(err).NotTo(HaveOccurred())
				Expect(result).NotTo(BeNil())
				Expect(result.Metadata["type"]).To(Equal("container_image"))
				Expect(result.Metadata["registry"]).To(Equal("registry.example.com"))
			})
		})
	})

	Describe("Health checks", func() {
		Context("when signer is healthy", func() {
			BeforeEach(func() {
				var err error
				signer, err = security.NewSigner(cfg)
				Expect(err).NotTo(HaveOccurred())
			})

			It("should pass health check", func() {
				err := signer.Health(ctx)
				Expect(err).NotTo(HaveOccurred())
			})
		})

		Context("when signing is disabled", func() {
			BeforeEach(func() {
				cfg.Security.Signing.Enabled = false
				var err error
				signer, err = security.NewSigner(cfg)
				Expect(err).To(HaveOccurred())
			})

			It("should fail health check", func() {
				// Signer creation should fail, so this test verifies the behavior
				Expect(signer).To(BeNil())
			})
		})
	})
})

// Traditional unit tests using testify for compatibility
type SignerTestSuite struct {
	suite.Suite
	config  *config.Config
	tempDir string
}

func (suite *SignerTestSuite) SetupTest() {
	var err error
	suite.tempDir, err = os.MkdirTemp("", "signer-test-*")
	suite.Require().NoError(err)

	suite.config = &config.Config{
		Security: config.SecurityConfig{
			Signing: config.SigningConfig{
				Enabled:  true,
				Method:   "key",
				KeyPath:  filepath.Join(suite.tempDir, "test.key"),
				CertPath: filepath.Join(suite.tempDir, "test.crt"),
			},
		},
	}
}

func (suite *SignerTestSuite) TearDownTest() {
	if suite.tempDir != "" {
		os.RemoveAll(suite.tempDir)
	}
}

func (suite *SignerTestSuite) TestNewSigner_Success() {
	signer, err := security.NewSigner(suite.config)
	suite.NoError(err)
	suite.NotNil(signer)
}

func (suite *SignerTestSuite) TestNewSigner_DisabledSigning() {
	suite.config.Security.Signing.Enabled = false
	_, err := security.NewSigner(suite.config)
	suite.Error(err)
	suite.Contains(err.Error(), "signing is disabled")
}

func (suite *SignerTestSuite) TestGenerateKeyPair_Success() {
	signer, err := security.NewSigner(suite.config)
	suite.Require().NoError(err)

	ctx := context.Background()
	privPath, pubPath, err := signer.GenerateKeyPair(ctx, suite.tempDir)
	suite.NoError(err)
	suite.FileExists(privPath)
	suite.FileExists(pubPath)
}

func TestSignerSuite(t *testing.T) {
	suite.Run(t, new(SignerTestSuite))
}