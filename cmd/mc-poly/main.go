package main

import (
	"context"
	"fmt"
	"os"

	"github.com/mchorfa/mc-poly-installer/cmd/mc-poly/cmd"
	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/sirupsen/logrus"
)

var (
	version = "dev"
	commit  = "none"
	date    = "unknown"
)

func main() {
	// Initialize configuration
	cfg, err := config.Load()
	if err != nil {
		logrus.WithError(err).Fatal("Failed to load configuration")
	}

	// Set log level
	logLevel, err := logrus.ParseLevel(cfg.Logging.Level)
	if err != nil {
		logLevel = logrus.InfoLevel
	}
	logrus.SetLevel(logLevel)

	// Set log format
	if cfg.Logging.Format == "json" {
		logrus.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logrus.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
			DisableColors: !cfg.Logging.EnableColors,
		})
	}

	// Create context
	ctx := context.Background()

	// Execute root command
	rootCmd := cmd.NewRootCommand(cfg, version, commit, date)
	if err := rootCmd.ExecuteContext(ctx); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
