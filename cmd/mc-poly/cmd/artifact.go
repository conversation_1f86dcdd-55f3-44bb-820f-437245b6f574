package cmd

import (
	"github.com/mchorfa/mc-poly-installer/pkg/artifact"
	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/spf13/cobra"
)

// NewArtifactCommand creates the artifact command
func NewArtifactCommand(cfg *config.Config) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "artifact",
		Short: "Manage artifacts with JFrog CLI",
		Long: `Manage artifacts using JFrog CLI for upload, download, scanning, 
and distribution across online and airgapped environments.`,
	}

	// Add subcommands
	cmd.AddCommand(
		newArtifactUploadCommand(cfg),
		newArtifactDownloadCommand(cfg),
		newArtifactScanCommand(cfg),
		newArtifactDistributeCommand(cfg),
		newArtifactStatusCommand(cfg),
	)

	return cmd
}

func newArtifactUploadCommand(cfg *config.Config) *cobra.Command {
	var (
		source      string
		target      string
		repository  string
		buildName   string
		buildNumber string
		parallel    int
		dryRun      bool
		recursive   bool
	)

	cmd := &cobra.Command{
		Use:   "upload",
		Short: "Upload artifacts to JFrog Artifactory",
		Long: `Upload artifacts to JFrog Artifactory with build information and metadata.
Supports parallel uploads and build-info collection for traceability.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return artifact.Upload(cfg, &artifact.UploadOptions{
				Source:      source,
				Target:      target,
				Repository:  repository,
				BuildName:   buildName,
				BuildNumber: buildNumber,
				Parallel:    parallel,
				DryRun:      dryRun,
				Recursive:   recursive,
			})
		},
	}

	cmd.Flags().StringVarP(&source, "source", "s", "", "source path pattern (required)")
	cmd.Flags().StringVarP(&target, "target", "t", "", "target path in repository")
	cmd.Flags().StringVarP(&repository, "repo", "r", "", "target repository")
	cmd.Flags().StringVar(&buildName, "build-name", "", "build name for build-info")
	cmd.Flags().StringVar(&buildNumber, "build-number", "", "build number for build-info")
	cmd.Flags().IntVar(&parallel, "threads", 3, "number of parallel upload threads")
	cmd.Flags().BoolVar(&dryRun, "dry-run", false, "perform a dry run")
	cmd.Flags().BoolVar(&recursive, "recursive", true, "recursive upload")

	cmd.MarkFlagRequired("source")

	return cmd
}

func newArtifactDownloadCommand(cfg *config.Config) *cobra.Command {
	var (
		source      string
		target      string
		repository  string
		buildName   string
		buildNumber string
		parallel    int
		validate    bool
		recursive   bool
	)

	cmd := &cobra.Command{
		Use:   "download",
		Short: "Download artifacts from JFrog Artifactory",
		Long: `Download artifacts from JFrog Artifactory with integrity validation.
Supports pattern matching and parallel downloads for efficiency.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return artifact.Download(cfg, &artifact.DownloadOptions{
				Source:      source,
				Target:      target,
				Repository:  repository,
				BuildName:   buildName,
				BuildNumber: buildNumber,
				Parallel:    parallel,
				Validate:    validate,
				Recursive:   recursive,
			})
		},
	}

	cmd.Flags().StringVarP(&source, "source", "s", "", "source path pattern (required)")
	cmd.Flags().StringVarP(&target, "target", "t", "", "target directory")
	cmd.Flags().StringVarP(&repository, "repo", "r", "", "source repository")
	cmd.Flags().StringVar(&buildName, "build-name", "", "build name filter")
	cmd.Flags().StringVar(&buildNumber, "build-number", "", "build number filter")
	cmd.Flags().IntVar(&parallel, "threads", 3, "number of parallel download threads")
	cmd.Flags().BoolVar(&validate, "validate", true, "validate artifact integrity")
	cmd.Flags().BoolVar(&recursive, "recursive", true, "recursive download")

	cmd.MarkFlagRequired("source")

	return cmd
}

func newArtifactScanCommand(cfg *config.Config) *cobra.Command {
	var (
		target      string
		repository  string
		buildName   string
		buildNumber string
		format      string
		outputFile  string
		fail        bool
	)

	cmd := &cobra.Command{
		Use:   "scan",
		Short: "Scan artifacts for security vulnerabilities",
		Long: `Scan artifacts using JFrog Xray for security vulnerabilities, license compliance,
and policy violations. Supports various output formats for integration.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return artifact.Scan(cfg, &artifact.ScanOptions{
				Target:      target,
				Repository:  repository,
				BuildName:   buildName,
				BuildNumber: buildNumber,
				Format:      format,
				OutputFile:  outputFile,
				Fail:        fail,
			})
		},
	}

	cmd.Flags().StringVarP(&target, "target", "t", "", "target to scan (path or build)")
	cmd.Flags().StringVarP(&repository, "repo", "r", "", "repository to scan")
	cmd.Flags().StringVar(&buildName, "build-name", "", "build name to scan")
	cmd.Flags().StringVar(&buildNumber, "build-number", "", "build number to scan")
	cmd.Flags().StringVarP(&format, "format", "f", "table", "output format (table, json, xml)")
	cmd.Flags().StringVarP(&outputFile, "output", "o", "", "output file path")
	cmd.Flags().BoolVar(&fail, "fail", true, "fail on security violations")

	return cmd
}

func newArtifactDistributeCommand(cfg *config.Config) *cobra.Command {
	var (
		buildName      string
		buildNumber    string
		target         string
		releaseBundle  string
		distributionID string
		dryRun         bool
		sign           bool
	)

	cmd := &cobra.Command{
		Use:   "distribute",
		Short: "Distribute artifacts to target environments",
		Long: `Distribute artifacts using JFrog Distribution to target environments.
Supports release bundles and distribution to airgapped environments.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return artifact.Distribute(cfg, &artifact.DistributeOptions{
				BuildName:      buildName,
				BuildNumber:    buildNumber,
				Target:         target,
				ReleaseBundle:  releaseBundle,
				DistributionID: distributionID,
				DryRun:         dryRun,
				Sign:           sign,
			})
		},
	}

	cmd.Flags().StringVar(&buildName, "build-name", "", "build name to distribute")
	cmd.Flags().StringVar(&buildNumber, "build-number", "", "build number to distribute")
	cmd.Flags().StringVarP(&target, "target", "t", "", "distribution target")
	cmd.Flags().StringVar(&releaseBundle, "release-bundle", "", "release bundle name")
	cmd.Flags().StringVar(&distributionID, "dist-id", "", "distribution ID")
	cmd.Flags().BoolVar(&dryRun, "dry-run", false, "perform a dry run")
	cmd.Flags().BoolVar(&sign, "sign", true, "sign the release bundle")

	return cmd
}

func newArtifactStatusCommand(cfg *config.Config) *cobra.Command {
	var (
		target         string
		repository     string
		buildName      string
		buildNumber    string
		distributionID string
		detailed       bool
	)

	cmd := &cobra.Command{
		Use:   "status",
		Short: "Check artifact status and metadata",
		Long: `Check the status and metadata of artifacts in JFrog Artifactory.
Shows build information, properties, and distribution status.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return artifact.Status(cfg, &artifact.StatusOptions{
				Target:         target,
				Repository:     repository,
				BuildName:      buildName,
				BuildNumber:    buildNumber,
				DistributionID: distributionID,
				Detailed:       detailed,
			})
		},
	}

	cmd.Flags().StringVarP(&target, "target", "t", "", "artifact path or pattern")
	cmd.Flags().StringVarP(&repository, "repo", "r", "", "repository name")
	cmd.Flags().StringVar(&buildName, "build-name", "", "build name")
	cmd.Flags().StringVar(&buildNumber, "build-number", "", "build number")
	cmd.Flags().StringVar(&distributionID, "dist-id", "", "distribution ID")
	cmd.Flags().BoolVar(&detailed, "detailed", false, "show detailed information")

	return cmd
}
