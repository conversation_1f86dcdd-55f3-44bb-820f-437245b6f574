package cmd

import (
	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/mchorfa/mc-poly-installer/pkg/pipeline"
	"github.com/spf13/cobra"
)

// NewPipelineCommand creates the pipeline command
func NewPipelineCommand(cfg *config.Config) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "pipeline",
		Short: "Manage CI/CD pipelines with Dagger",
		Long: `Manage CI/CD pipelines using Dagger engine for consistent builds 
across online and airgapped environments.`,
	}

	// Add subcommands
	cmd.AddCommand(
		newPipelineCreateCommand(cfg),
		newPipelineDeployCommand(cfg),
		newPipelineValidateCommand(cfg),
		newPipelineStatusCommand(cfg),
		newPipelineLogsCommand(cfg),
	)

	return cmd
}

func newPipelineCreateCommand(cfg *config.Config) *cobra.Command {
	var (
		name        string
		template    string
		outputPath  string
		environment string
	)

	cmd := &cobra.Command{
		Use:   "create",
		Short: "Create a new pipeline configuration",
		Long: `Create a new pipeline configuration from a template or from scratch.
Supports various templates for common deployment patterns.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return pipeline.Create(cfg, &pipeline.CreateOptions{
				Name:        name,
				Template:    template,
				OutputPath:  outputPath,
				Environment: environment,
			})
		},
	}

	cmd.Flags().StringVarP(&name, "name", "n", "", "pipeline name (required)")
	cmd.Flags().StringVarP(&template, "template", "t", "default", "pipeline template (default, webapp, service, library)")
	cmd.Flags().StringVarP(&outputPath, "output", "o", "", "output path for pipeline configuration")
	cmd.Flags().StringVarP(&environment, "env", "e", "online", "target environment (online, airgapped)")

	cmd.MarkFlagRequired("name")

	return cmd
}

func newPipelineDeployCommand(cfg *config.Config) *cobra.Command {
	var (
		configFile  string
		environment string
		dryRun      bool
		parallel    bool
	)

	cmd := &cobra.Command{
		Use:   "deploy",
		Short: "Deploy pipeline to target environment",
		Long: `Deploy a pipeline configuration to the target environment.
Supports both online and airgapped deployments with proper artifact transfer.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return pipeline.Deploy(cfg, &pipeline.DeployOptions{
				ConfigFile:  configFile,
				Environment: environment,
				DryRun:      dryRun,
				Parallel:    parallel,
			})
		},
	}

	cmd.Flags().StringVarP(&configFile, "config", "c", "", "pipeline configuration file (required)")
	cmd.Flags().StringVarP(&environment, "env", "e", "online", "target environment (online, airgapped)")
	cmd.Flags().BoolVar(&dryRun, "dry-run", false, "perform a dry run without actual deployment")
	cmd.Flags().BoolVar(&parallel, "parallel", true, "enable parallel execution")

	cmd.MarkFlagRequired("config")

	return cmd
}

func newPipelineValidateCommand(cfg *config.Config) *cobra.Command {
	var (
		configFile string
		strict     bool
	)

	cmd := &cobra.Command{
		Use:   "validate",
		Short: "Validate pipeline configuration",
		Long: `Validate pipeline configuration for syntax, dependencies, and compatibility.
Checks against security policies and compliance requirements.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return pipeline.Validate(cfg, &pipeline.ValidateOptions{
				ConfigFile: configFile,
				Strict:     strict,
			})
		},
	}

	cmd.Flags().StringVarP(&configFile, "config", "c", "", "pipeline configuration file (required)")
	cmd.Flags().BoolVar(&strict, "strict", false, "enable strict validation mode")

	cmd.MarkFlagRequired("config")

	return cmd
}

func newPipelineStatusCommand(cfg *config.Config) *cobra.Command {
	var (
		pipelineID  string
		environment string
		follow      bool
	)

	cmd := &cobra.Command{
		Use:   "status",
		Short: "Check pipeline execution status",
		Long: `Check the status of pipeline executions.
Shows current state, progress, and any errors or warnings.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return pipeline.Status(cfg, &pipeline.StatusOptions{
				PipelineID:  pipelineID,
				Environment: environment,
				Follow:      follow,
			})
		},
	}

	cmd.Flags().StringVarP(&pipelineID, "id", "i", "", "pipeline execution ID")
	cmd.Flags().StringVarP(&environment, "env", "e", "online", "target environment")
	cmd.Flags().BoolVarP(&follow, "follow", "f", false, "follow status updates")

	return cmd
}

func newPipelineLogsCommand(cfg *config.Config) *cobra.Command {
	var (
		pipelineID  string
		environment string
		follow      bool
		tail        int
	)

	cmd := &cobra.Command{
		Use:   "logs",
		Short: "View pipeline execution logs",
		Long: `View logs from pipeline executions.
Supports real-time log streaming and historical log retrieval.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return pipeline.Logs(cfg, &pipeline.LogsOptions{
				PipelineID:  pipelineID,
				Environment: environment,
				Follow:      follow,
				Tail:        tail,
			})
		},
	}

	cmd.Flags().StringVarP(&pipelineID, "id", "i", "", "pipeline execution ID")
	cmd.Flags().StringVarP(&environment, "env", "e", "online", "target environment")
	cmd.Flags().BoolVarP(&follow, "follow", "f", false, "follow log output")
	cmd.Flags().IntVar(&tail, "tail", 100, "number of lines to show from the end")

	return cmd
}
