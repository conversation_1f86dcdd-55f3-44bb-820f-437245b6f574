package cmd

import (
	"fmt"
	"os"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/spf13/cobra"
	"gopkg.in/yaml.v3"
)

// NewConfigCommand creates the config command
func NewConfigCommand(cfg *config.Config) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "config",
		Short: "Manage application configuration",
		Long: `Manage application configuration including environments, security settings,
and tool integrations.`,
	}

	// Add subcommands
	cmd.AddCommand(
		newConfigViewCommand(cfg),
		newConfigEditCommand(cfg),
		newConfigValidateCommand(cfg),
		newConfigExportCommand(cfg),
		newConfigImportCommand(cfg),
		newConfigResetCommand(cfg),
	)

	return cmd
}

func newConfigViewCommand(cfg *config.Config) *cobra.Command {
	var (
		format string
		mask   bool
	)

	cmd := &cobra.Command{
		Use:   "view",
		Short: "View current configuration",
		Long: `View the current application configuration.
Supports different output formats and can mask sensitive values.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return viewConfig(cfg, format, mask)
		},
	}

	cmd.Flags().StringVarP(&format, "format", "f", "yaml", "output format (yaml, json)")
	cmd.Flags().BoolVar(&mask, "mask-secrets", true, "mask sensitive values")

	return cmd
}

func newConfigEditCommand(cfg *config.Config) *cobra.Command {
	var (
		editor string
		inline bool
	)

	cmd := &cobra.Command{
		Use:   "edit",
		Short: "Edit configuration file",
		Long: `Edit the configuration file using your preferred editor.
Opens the configuration file in the specified editor or system default.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return editConfig(cfg, editor, inline)
		},
	}

	cmd.Flags().StringVarP(&editor, "editor", "e", "", "editor to use (defaults to $EDITOR)")
	cmd.Flags().BoolVar(&inline, "inline", false, "edit configuration inline")

	return cmd
}

func newConfigValidateCommand(cfg *config.Config) *cobra.Command {
	var (
		strict bool
		fix    bool
	)

	cmd := &cobra.Command{
		Use:   "validate",
		Short: "Validate configuration",
		Long: `Validate the configuration for syntax, required fields, and logical consistency.
Can automatically fix common issues when requested.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return validateConfig(cfg, strict, fix)
		},
	}

	cmd.Flags().BoolVar(&strict, "strict", false, "enable strict validation")
	cmd.Flags().BoolVar(&fix, "fix", false, "attempt to fix validation issues")

	return cmd
}

func newConfigExportCommand(cfg *config.Config) *cobra.Command {
	var (
		outputFile   string
		format       string
		includeKeys  bool
		maskSecrets  bool
		environment  string
	)

	cmd := &cobra.Command{
		Use:   "export",
		Short: "Export configuration",
		Long: `Export configuration to a file or stdout.
Supports filtering by environment and can include or exclude sensitive data.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return exportConfig(cfg, outputFile, format, includeKeys, maskSecrets, environment)
		},
	}

	cmd.Flags().StringVarP(&outputFile, "output", "o", "", "output file (defaults to stdout)")
	cmd.Flags().StringVarP(&format, "format", "f", "yaml", "output format (yaml, json)")
	cmd.Flags().BoolVar(&includeKeys, "include-keys", false, "include cryptographic keys")
	cmd.Flags().BoolVar(&maskSecrets, "mask-secrets", true, "mask sensitive values")
	cmd.Flags().StringVarP(&environment, "env", "e", "", "export specific environment only")

	return cmd
}

func newConfigImportCommand(cfg *config.Config) *cobra.Command {
	var (
		inputFile   string
		merge       bool
		validate    bool
		backup      bool
	)

	cmd := &cobra.Command{
		Use:   "import",
		Short: "Import configuration",
		Long: `Import configuration from a file.
Can merge with existing configuration or replace it entirely.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return importConfig(cfg, inputFile, merge, validate, backup)
		},
	}

	cmd.Flags().StringVarP(&inputFile, "input", "i", "", "input file (required)")
	cmd.Flags().BoolVar(&merge, "merge", false, "merge with existing configuration")
	cmd.Flags().BoolVar(&validate, "validate", true, "validate imported configuration")
	cmd.Flags().BoolVar(&backup, "backup", true, "create backup of current configuration")

	cmd.MarkFlagRequired("input")

	return cmd
}

func newConfigResetCommand(cfg *config.Config) *cobra.Command {
	var (
		confirm     bool
		backup      bool
		environment string
	)

	cmd := &cobra.Command{
		Use:   "reset",
		Short: "Reset configuration to defaults",
		Long: `Reset configuration to default values.
Can reset entire configuration or specific environment settings.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return resetConfig(cfg, confirm, backup, environment)
		},
	}

	cmd.Flags().BoolVar(&confirm, "confirm", false, "confirm reset operation")
	cmd.Flags().BoolVar(&backup, "backup", true, "create backup before reset")
	cmd.Flags().StringVarP(&environment, "env", "e", "", "reset specific environment only")

	return cmd
}

// Implementation functions

func viewConfig(cfg *config.Config, format string, mask bool) error {
	// Create a copy for masking if needed
	displayCfg := *cfg
	
	if mask {
		maskSensitiveValues(&displayCfg)
	}

	var output []byte
	var err error

	switch format {
	case "json":
		// Note: would need to import encoding/json
		fmt.Println("JSON output not implemented yet")
		return nil
	case "yaml":
		output, err = yaml.Marshal(&displayCfg)
		if err != nil {
			return fmt.Errorf("error marshaling configuration: %w", err)
		}
	default:
		return fmt.Errorf("unsupported format: %s", format)
	}

	fmt.Print(string(output))
	return nil
}

func editConfig(cfg *config.Config, editor string, inline bool) error {
	if inline {
		fmt.Println("Inline editing not implemented yet")
		return nil
	}

	// Get editor from environment if not specified
	if editor == "" {
		editor = os.Getenv("EDITOR")
		if editor == "" {
			editor = "vi" // fallback
		}
	}

	fmt.Printf("Opening configuration in %s...\n", editor)
	fmt.Println("Edit functionality would open the config file in the specified editor")
	
	return nil
}

func validateConfig(cfg *config.Config, strict bool, fix bool) error {
	fmt.Printf("Validating configuration (strict: %t, fix: %t)...\n", strict, fix)
	
	if err := cfg.Validate(); err != nil {
		fmt.Printf("❌ Validation failed: %v\n", err)
		
		if fix {
			fmt.Println("🔧 Attempting to fix issues...")
			// Would implement auto-fix logic here
		}
		
		return err
	}
	
	fmt.Println("✅ Configuration is valid")
	return nil
}

func exportConfig(cfg *config.Config, outputFile, format string, includeKeys, maskSecrets bool, environment string) error {
	fmt.Printf("Exporting configuration (format: %s, file: %s)\n", format, outputFile)
	
	// Create export copy
	exportCfg := *cfg
	
	if maskSecrets && !includeKeys {
		maskSensitiveValues(&exportCfg)
	}
	
	// Filter by environment if specified
	if environment != "" {
		// Would implement environment filtering
		fmt.Printf("Filtering for environment: %s\n", environment)
	}
	
	var output []byte
	var err error
	
	switch format {
	case "yaml":
		output, err = yaml.Marshal(&exportCfg)
	default:
		return fmt.Errorf("unsupported format: %s", format)
	}
	
	if err != nil {
		return fmt.Errorf("error marshaling configuration: %w", err)
	}
	
	if outputFile == "" {
		fmt.Print(string(output))
	} else {
		if err := os.WriteFile(outputFile, output, 0644); err != nil {
			return fmt.Errorf("error writing to file: %w", err)
		}
		fmt.Printf("Configuration exported to %s\n", outputFile)
	}
	
	return nil
}

func importConfig(cfg *config.Config, inputFile string, merge, validate, backup bool) error {
	fmt.Printf("Importing configuration from %s (merge: %t, validate: %t, backup: %t)\n", 
		inputFile, merge, validate, backup)
	
	// Read input file
	data, err := os.ReadFile(inputFile)
	if err != nil {
		return fmt.Errorf("error reading input file: %w", err)
	}
	
	// Parse configuration
	var newCfg config.Config
	if err := yaml.Unmarshal(data, &newCfg); err != nil {
		return fmt.Errorf("error parsing configuration: %w", err)
	}
	
	// Validate if requested
	if validate {
		if err := newCfg.Validate(); err != nil {
			return fmt.Errorf("imported configuration is invalid: %w", err)
		}
	}
	
	// Create backup if requested
	if backup {
		fmt.Println("Creating backup of current configuration...")
		// Would implement backup logic
	}
	
	// Merge or replace
	if merge {
		fmt.Println("Merging configurations...")
		// Would implement merge logic
	} else {
		fmt.Println("Replacing configuration...")
		*cfg = newCfg
	}
	
	fmt.Println("✅ Configuration imported successfully")
	return nil
}

func resetConfig(cfg *config.Config, confirm, backup bool, environment string) error {
	if !confirm {
		fmt.Println("Reset operation requires --confirm flag")
		return nil
	}
	
	if backup {
		fmt.Println("Creating backup before reset...")
		// Would implement backup logic
	}
	
	if environment != "" {
		fmt.Printf("Resetting environment: %s\n", environment)
		// Would reset specific environment
	} else {
		fmt.Println("Resetting entire configuration to defaults...")
		// Would reset to defaults
	}
	
	fmt.Println("✅ Configuration reset completed")
	return nil
}

func maskSensitiveValues(cfg *config.Config) {
	// Mask sensitive values
	if cfg.Environments.Online.GitLab.Token != "" {
		cfg.Environments.Online.GitLab.Token = "***masked***"
	}
	if cfg.Environments.Online.JFrog.Password != "" {
		cfg.Environments.Online.JFrog.Password = "***masked***"
	}
	if cfg.Environments.Airgapped.JFrog.Password != "" {
		cfg.Environments.Airgapped.JFrog.Password = "***masked***"
	}
	if cfg.Security.Signing.Passphrase != "" {
		cfg.Security.Signing.Passphrase = "***masked***"
	}
}
