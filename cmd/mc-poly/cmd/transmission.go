package cmd

import (
	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/mchorfa/mc-poly-installer/pkg/transmission"
	"github.com/spf13/cobra"
)

// NewTransmissionCommand creates the transmission command
func NewTransmissionCommand(cfg *config.Config) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "transmission",
		Short: "Manage transmission station operations",
		Long: `Manage transmission station operations for secure transfer of artifacts
and bundles between online and airgapped environments.`,
		Aliases: []string{"trans", "station"},
	}

	// Add subcommands
	cmd.AddCommand(
		newTransmissionSetupCommand(cfg),
		newTransmissionValidateCommand(cfg),
		newTransmissionMonitorCommand(cfg),
		newTransmissionAuditCommand(cfg),
		newTransmissionStatusCommand(cfg),
	)

	return cmd
}

func newTransmissionSetupCommand(cfg *config.Config) *cobra.Command {
	var (
		endpoint    string
		authMethod  string
		certPath    string
		keyPath     string
		caPath      string
		timeout     int
		retries     int
		interactive bool
	)

	cmd := &cobra.Command{
		Use:   "setup",
		Short: "Setup transmission station configuration",
		Long: `Setup and configure the transmission station for secure transfers.
Includes certificate management and connection validation.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return transmission.Setup(cfg, &transmission.SetupOptions{
				Endpoint:    endpoint,
				AuthMethod:  authMethod,
				CertPath:    certPath,
				KeyPath:     keyPath,
				CAPath:      caPath,
				Timeout:     timeout,
				Retries:     retries,
				Interactive: interactive,
			})
		},
	}

	cmd.Flags().StringVarP(&endpoint, "endpoint", "e", "", "transmission station endpoint")
	cmd.Flags().StringVar(&authMethod, "auth-method", "mutual-tls", "authentication method")
	cmd.Flags().StringVar(&certPath, "cert", "", "client certificate path")
	cmd.Flags().StringVar(&keyPath, "key", "", "client private key path")
	cmd.Flags().StringVar(&caPath, "ca", "", "CA certificate path")
	cmd.Flags().IntVar(&timeout, "timeout", 300, "connection timeout in seconds")
	cmd.Flags().IntVar(&retries, "retries", 3, "number of retry attempts")
	cmd.Flags().BoolVarP(&interactive, "interactive", "i", false, "interactive setup mode")

	return cmd
}

func newTransmissionValidateCommand(cfg *config.Config) *cobra.Command {
	var (
		transferID   string
		artifactPath string
		bundlePath   string
		checksumOnly bool
		deep         bool
	)

	cmd := &cobra.Command{
		Use:   "validate",
		Short: "Validate transfer security and integrity",
		Long: `Validate the security and integrity of transfers through the transmission station.
Includes checksum verification, signature validation, and policy compliance.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return transmission.Validate(cfg, &transmission.ValidateOptions{
				TransferID:   transferID,
				ArtifactPath: artifactPath,
				BundlePath:   bundlePath,
				ChecksumOnly: checksumOnly,
				Deep:         deep,
			})
		},
	}

	cmd.Flags().StringVar(&transferID, "transfer-id", "", "transfer ID to validate")
	cmd.Flags().StringVar(&artifactPath, "artifact", "", "artifact path to validate")
	cmd.Flags().StringVar(&bundlePath, "bundle", "", "bundle path to validate")
	cmd.Flags().BoolVar(&checksumOnly, "checksum-only", false, "validate checksums only")
	cmd.Flags().BoolVar(&deep, "deep", false, "perform deep validation")

	return cmd
}

func newTransmissionMonitorCommand(cfg *config.Config) *cobra.Command {
	var (
		follow      bool
		interval    int
		transferID  string
		showMetrics bool
	)

	cmd := &cobra.Command{
		Use:   "monitor",
		Short: "Monitor transmission station operations",
		Long: `Monitor transmission station operations and health in real-time.
Shows transfer progress, connection status, and performance metrics.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return transmission.Monitor(cfg, &transmission.MonitorOptions{
				Follow:      follow,
				Interval:    interval,
				TransferID:  transferID,
				ShowMetrics: showMetrics,
			})
		},
	}

	cmd.Flags().BoolVarP(&follow, "follow", "f", false, "follow monitoring output")
	cmd.Flags().IntVar(&interval, "interval", 5, "monitoring interval in seconds")
	cmd.Flags().StringVar(&transferID, "transfer-id", "", "specific transfer to monitor")
	cmd.Flags().BoolVar(&showMetrics, "metrics", false, "show performance metrics")

	return cmd
}

func newTransmissionAuditCommand(cfg *config.Config) *cobra.Command {
	var (
		startTime   string
		endTime     string
		transferID  string
		userID      string
		format      string
		outputFile  string
		includeData bool
	)

	cmd := &cobra.Command{
		Use:   "audit",
		Short: "Review transmission audit logs",
		Long: `Review transmission audit logs for compliance and security analysis.
Supports filtering by time range, user, and transfer ID.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return transmission.Audit(cfg, &transmission.AuditOptions{
				StartTime:   startTime,
				EndTime:     endTime,
				TransferID:  transferID,
				UserID:      userID,
				Format:      format,
				OutputFile:  outputFile,
				IncludeData: includeData,
			})
		},
	}

	cmd.Flags().StringVar(&startTime, "start", "", "start time (RFC3339 format)")
	cmd.Flags().StringVar(&endTime, "end", "", "end time (RFC3339 format)")
	cmd.Flags().StringVar(&transferID, "transfer-id", "", "filter by transfer ID")
	cmd.Flags().StringVar(&userID, "user", "", "filter by user ID")
	cmd.Flags().StringVarP(&format, "format", "f", "table", "output format (table, json, csv)")
	cmd.Flags().StringVarP(&outputFile, "output", "o", "", "output file path")
	cmd.Flags().BoolVar(&includeData, "include-data", false, "include transfer data in output")

	return cmd
}

func newTransmissionStatusCommand(cfg *config.Config) *cobra.Command {
	var (
		detailed   bool
		health     bool
		transferID string
	)

	cmd := &cobra.Command{
		Use:   "status",
		Short: "Check transmission station status",
		Long: `Check the status and connectivity of the transmission station.
Shows connection health, active transfers, and system status.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return transmission.Status(cfg, &transmission.StatusOptions{
				Detailed:   detailed,
				Health:     health,
				TransferID: transferID,
			})
		},
	}

	cmd.Flags().BoolVar(&detailed, "detailed", false, "show detailed status information")
	cmd.Flags().BoolVar(&health, "health", false, "perform health check")
	cmd.Flags().StringVar(&transferID, "transfer-id", "", "specific transfer status")

	return cmd
}
