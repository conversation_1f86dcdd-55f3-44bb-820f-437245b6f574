package cmd

import (
	"fmt"
	"os"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/mchorfa/mc-poly-installer/pkg/tui"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

// NewRootCommand creates the root command for mc-poly
func NewRootCommand(cfg *config.Config, version, commit, date string) *cobra.Command {
	var configFile string
	var verbose bool

	rootCmd := &cobra.Command{
		Use:   "mc-poly",
		Short: "Universal Airgapped Pipeline CLI/TUI",
		Long: `MC Poly Installer - A comprehensive command-line and terminal user interface tool 
for managing software delivery pipelines across airgapped environments. 

Integrates GitLab CI/CD, Dagger, Porter (CNAB), and JFrog CLI for secure, 
enterprise-grade deployment workflows.`,
		Version: fmt.Sprintf("%s (commit: %s, date: %s)", version, commit, date),
		PersistentPreRun: func(cmd *cobra.Command, args []string) {
			if verbose {
				fmt.Fprintf(os.Stderr, "Using config file: %s\n", viper.ConfigFileUsed())
			}
		},
	}

	// Global flags
	rootCmd.PersistentFlags().StringVar(&configFile, "config", "", "config file (default is $HOME/.mc-poly/config.yaml)")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "verbose output")

	// TUI command (default when no subcommand is provided)
	tuiCmd := &cobra.Command{
		Use:   "tui",
		Short: "Launch interactive terminal interface",
		Long:  "Launch the interactive terminal user interface for managing pipelines and operations.",
		RunE: func(cmd *cobra.Command, args []string) error {
			return tui.Run(cfg)
		},
	}

	// Initialize subcommands
	initCmd := NewInitCommand(cfg)
	configCmd := NewConfigCommand(cfg)
	pipelineCmd := NewPipelineCommand(cfg)
	bundleCmd := NewBundleCommand(cfg)
	artifactCmd := NewArtifactCommand(cfg)
	transmissionCmd := NewTransmissionCommand(cfg)
	versionCmd := NewVersionCommand(version, commit, date)

	// Add subcommands
	rootCmd.AddCommand(
		tuiCmd,
		initCmd,
		configCmd,
		pipelineCmd,
		bundleCmd,
		artifactCmd,
		transmissionCmd,
		versionCmd,
	)

	// Default to TUI if no subcommand is provided
	rootCmd.RunE = func(cmd *cobra.Command, args []string) error {
		if len(args) == 0 {
			return tui.Run(cfg)
		}
		return cmd.Help()
	}

	return rootCmd
}

// NewVersionCommand creates the version command
func NewVersionCommand(version, commit, date string) *cobra.Command {
	return &cobra.Command{
		Use:   "version",
		Short: "Print version information",
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Printf("mc-poly version %s\n", version)
			fmt.Printf("Git commit: %s\n", commit)
			fmt.Printf("Build date: %s\n", date)
		},
	}
}
