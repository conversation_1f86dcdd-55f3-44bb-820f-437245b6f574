package cmd

import (
	"github.com/mchorfa/mc-poly-installer/pkg/bundle"
	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/spf13/cobra"
)

// NewBundleCommand creates the bundle command
func NewBundleCommand(cfg *config.Config) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "bundle",
		Short: "Manage CNAB bundles with Porter",
		Long: `Manage Cloud Native Application Bundles (CNAB) using Porter for 
portable application packaging and deployment across environments.`,
	}

	// Add subcommands
	cmd.AddCommand(
		newBundleCreateCommand(cfg),
		newBundlePublishCommand(cfg),
		newBundleTransferCommand(cfg),
		newBundleInstallCommand(cfg),
		newBundleStatusCommand(cfg),
	)

	return cmd
}

func newBundleCreateCommand(cfg *config.Config) *cobra.Command {
	var (
		name        string
		version     string
		description string
		template    string
		outputPath  string
		mixins      []string
	)

	cmd := &cobra.Command{
		Use:   "create",
		Short: "Create a new CNAB bundle",
		Long: `Create a new Cloud Native Application Bundle (CNAB) with Porter.
Supports various mixins and templates for different application types.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return bundle.Create(cfg, &bundle.CreateOptions{
				Name:        name,
				Version:     version,
				Description: description,
				Template:    template,
				OutputPath:  outputPath,
				Mixins:      mixins,
			})
		},
	}

	cmd.Flags().StringVarP(&name, "name", "n", "", "bundle name (required)")
	cmd.Flags().StringVarP(&version, "version", "v", "0.1.0", "bundle version")
	cmd.Flags().StringVarP(&description, "description", "d", "", "bundle description")
	cmd.Flags().StringVarP(&template, "template", "t", "default", "bundle template")
	cmd.Flags().StringVarP(&outputPath, "output", "o", "", "output directory")
	cmd.Flags().StringSliceVarP(&mixins, "mixins", "m", []string{"exec"}, "Porter mixins to include")

	cmd.MarkFlagRequired("name")

	return cmd
}

func newBundlePublishCommand(cfg *config.Config) *cobra.Command {
	var (
		bundlePath string
		registry   string
		tag        string
		sign       bool
	)

	cmd := &cobra.Command{
		Use:   "publish",
		Short: "Publish bundle to OCI registry",
		Long: `Publish a CNAB bundle to an OCI-compatible registry.
Supports signing and attestation for secure distribution.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return bundle.Publish(cfg, &bundle.PublishOptions{
				BundlePath: bundlePath,
				Registry:   registry,
				Tag:        tag,
				Sign:       sign,
			})
		},
	}

	cmd.Flags().StringVarP(&bundlePath, "bundle", "b", "", "path to bundle directory (required)")
	cmd.Flags().StringVarP(&registry, "registry", "r", "", "target registry (defaults to config)")
	cmd.Flags().StringVarP(&tag, "tag", "t", "latest", "bundle tag")
	cmd.Flags().BoolVar(&sign, "sign", true, "sign the bundle")

	cmd.MarkFlagRequired("bundle")

	return cmd
}

func newBundleTransferCommand(cfg *config.Config) *cobra.Command {
	var (
		bundleRef   string
		destination string
		validate    bool
		compress    bool
	)

	cmd := &cobra.Command{
		Use:   "transfer",
		Short: "Transfer bundle through transmission station",
		Long: `Transfer a CNAB bundle through the transmission station to the airgapped environment.
Includes validation and integrity checks during transfer.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return bundle.Transfer(cfg, &bundle.TransferOptions{
				BundleRef:   bundleRef,
				Destination: destination,
				Validate:    validate,
				Compress:    compress,
			})
		},
	}

	cmd.Flags().StringVarP(&bundleRef, "bundle", "b", "", "bundle reference (required)")
	cmd.Flags().StringVarP(&destination, "destination", "d", "airgapped", "destination environment")
	cmd.Flags().BoolVar(&validate, "validate", true, "validate bundle integrity")
	cmd.Flags().BoolVar(&compress, "compress", true, "compress bundle for transfer")

	cmd.MarkFlagRequired("bundle")

	return cmd
}

func newBundleInstallCommand(cfg *config.Config) *cobra.Command {
	var (
		bundleRef   string
		environment string
		parameters  []string
		credentials []string
		dryRun      bool
	)

	cmd := &cobra.Command{
		Use:   "install",
		Short: "Install bundle in target environment",
		Long: `Install a CNAB bundle in the target environment.
Supports parameter and credential injection for customization.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return bundle.Install(cfg, &bundle.InstallOptions{
				BundleRef:   bundleRef,
				Environment: environment,
				Parameters:  parameters,
				Credentials: credentials,
				DryRun:      dryRun,
			})
		},
	}

	cmd.Flags().StringVarP(&bundleRef, "bundle", "b", "", "bundle reference (required)")
	cmd.Flags().StringVarP(&environment, "env", "e", "airgapped", "target environment")
	cmd.Flags().StringSliceVarP(&parameters, "param", "p", []string{}, "bundle parameters (key=value)")
	cmd.Flags().StringSliceVarP(&credentials, "cred", "c", []string{}, "bundle credentials")
	cmd.Flags().BoolVar(&dryRun, "dry-run", false, "perform a dry run")

	cmd.MarkFlagRequired("bundle")

	return cmd
}

func newBundleStatusCommand(cfg *config.Config) *cobra.Command {
	var (
		installation string
		environment  string
		detailed     bool
	)

	cmd := &cobra.Command{
		Use:   "status",
		Short: "Check bundle installation status",
		Long: `Check the status of bundle installations.
Shows installation state, outputs, and any errors.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return bundle.Status(cfg, &bundle.StatusOptions{
				Installation: installation,
				Environment:  environment,
				Detailed:     detailed,
			})
		},
	}

	cmd.Flags().StringVarP(&installation, "installation", "i", "", "installation name")
	cmd.Flags().StringVarP(&environment, "env", "e", "airgapped", "target environment")
	cmd.Flags().BoolVar(&detailed, "detailed", false, "show detailed status information")

	return cmd
}
