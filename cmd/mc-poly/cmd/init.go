package cmd

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/spf13/cobra"
)

// NewInitCommand creates the init command
func NewInitCommand(cfg *config.Config) *cobra.Command {
	var (
		force      bool
		configPath string
	)

	cmd := &cobra.Command{
		Use:   "init",
		Short: "Initialize MC Poly configuration",
		Long: `Initialize MC Poly configuration by creating a default configuration file
and setting up the necessary directory structure.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runInit(cfg, force, configPath)
		},
	}

	cmd.Flags().BoolVarP(&force, "force", "f", false, "force overwrite existing configuration")
	cmd.Flags().StringVarP(&configPath, "config-path", "c", "", "path to create configuration file (default: ~/.mc-poly/config.yaml)")

	return cmd
}

func runInit(cfg *config.Config, force bool, configPath string) error {
	// Determine config path
	if configPath == "" {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return fmt.Errorf("error getting home directory: %w", err)
		}
		configPath = filepath.Join(homeDir, ".mc-poly", "config.yaml")
	}

	// Check if config already exists
	if !force {
		if _, err := os.Stat(configPath); err == nil {
			return fmt.Errorf("configuration file already exists at %s (use --force to overwrite)", configPath)
		}
	}

	// Create default configuration
	defaultConfig := createDefaultConfig()

	// Create config directory
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("error creating config directory: %w", err)
	}

	// Save configuration
	if err := defaultConfig.Save(configPath); err != nil {
		return fmt.Errorf("error saving configuration: %w", err)
	}

	// Create additional directories
	dirs := []string{
		filepath.Join(configDir, "pipelines"),
		filepath.Join(configDir, "bundles"),
		filepath.Join(configDir, "artifacts"),
		filepath.Join(configDir, "logs"),
		filepath.Join(configDir, "cache"),
		filepath.Join(configDir, "keys"),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("error creating directory %s: %w", dir, err)
		}
	}

	fmt.Printf("✅ MC Poly configuration initialized successfully!\n")
	fmt.Printf("📁 Configuration saved to: %s\n", configPath)
	fmt.Printf("🔧 Edit the configuration file to customize settings for your environment.\n")
	fmt.Printf("🚀 Run 'mc-poly tui' to launch the interactive interface.\n")

	return nil
}

func createDefaultConfig() *config.Config {
	return &config.Config{
		Environments: config.EnvironmentsConfig{
			Online: config.OnlineEnvironment{
				GitLab: config.GitLabConfig{
					URL:   "https://gitlab.example.com",
					Token: "${GITLAB_TOKEN}",
					Projects: map[string]string{
						"main": "group/project",
					},
				},
				JFrog: config.JFrogConfig{
					URL:        "https://company.jfrog.io",
					Username:   "${JFROG_USER}",
					Password:   "${JFROG_PASSWORD}",
					Repository: "docker-local",
				},
			},
			Transmission: config.TransmissionEnvironment{
				Endpoint: "https://transmission.secure.zone",
				Auth: config.AuthConfig{
					Method:   "mutual-tls",
					CertPath: "~/.mc-poly/keys/client.crt",
					KeyPath:  "~/.mc-poly/keys/client.key",
					CAPath:   "~/.mc-poly/keys/ca.crt",
				},
				Timeout: 300,
				Retries: 3,
			},
			Airgapped: config.AirgappedEnvironment{
				JFrog: config.JFrogConfig{
					URL:        "https://internal.jfrog.local",
					Username:   "${AIRGAP_JFROG_USER}",
					Password:   "${AIRGAP_JFROG_PASSWORD}",
					Repository: "docker-local",
				},
			},
		},
		Security: config.SecurityConfig{
			Signing: config.SigningConfig{
				Enabled:  true,
				Method:   "sigstore",
				KeyPath:  "~/.mc-poly/keys/signing.key",
				CertPath: "~/.mc-poly/keys/signing.crt",
			},
			Verification: config.VerificationConfig{
				Required:  true,
				Policies:  []string{"slsa4", "sbom"},
				TrustRoot: "~/.mc-poly/keys/trust-root.json",
			},
			Encryption: config.EncryptionConfig{
				Enabled:   true,
				Algorithm: "AES-256-GCM",
				KeyPath:   "~/.mc-poly/keys/encryption.key",
			},
		},
		Dagger: config.DaggerConfig{
			EngineVersion: "0.13.0",
			CachePolicy:   "aggressive",
			Registry:      "company.jfrog.io/docker",
			Modules: map[string]string{
				"build":  "github.com/company/dagger-modules/build",
				"deploy": "github.com/company/dagger-modules/deploy",
				"test":   "github.com/company/dagger-modules/test",
			},
		},
		Porter: config.PorterConfig{
			Registry:      "company.jfrog.io/docker",
			DefaultDriver: "docker",
			Mixins:        []string{"exec", "docker", "kubernetes", "terraform"},
			Bundles: map[string]string{
				"webapp":    "company.jfrog.io/docker/webapp-bundle:latest",
				"database":  "company.jfrog.io/docker/db-bundle:latest",
				"monitoring": "company.jfrog.io/docker/monitoring-bundle:latest",
			},
		},
		Compliance: config.ComplianceConfig{
			AuditRetention:      "7y",
			SBOMFormat:          []string{"spdx", "cyclonedx"},
			AttestationRequired: true,
			ReportPath:          "~/.mc-poly/reports",
		},
		Logging: config.LoggingConfig{
			Level:        "info",
			Format:       "text",
			EnableColors: true,
			Output:       "~/.mc-poly/logs/mc-poly.log",
		},
		TUI: config.TUIConfig{
			Theme:       "default",
			RefreshRate: 60,
			MouseMode:   true,
		},
	}
}
