// Package: jfrog
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:jfrog-types:2025-01-27T10:00:00.123Z
//
// Purpose: JFrog data types and structures for artifact management and security scanning
// Governance: MCStack v13.5 compliance with enterprise security policies
// Security: Type safety for artifact signing, vulnerability scanning, and supply chain security
package jfrog

import "github.com/mchorfa/mc-poly-installer/pkg/config"

// Client represents a JFrog CLI client
type Client struct {
	config *config.Config
	// Would contain JFrog client configuration
}

// ArtifactoryInfo represents Artifactory instance information
type ArtifactoryInfo struct {
	Version     string `json:"version"`
	Revision    string `json:"revision"`
	License     string `json:"license"`
	BuildNumber string `json:"buildNumber"`
}

// Repository represents an Artifactory repository
type Repository struct {
	Key         string `json:"key"`
	Type        string `json:"type"`
	URL         string `json:"url"`
	Description string `json:"description"`
	PackageType string `json:"packageType"`
}

// ArtifactInfo represents artifact information
type ArtifactInfo struct {
	URI               string                 `json:"uri"`
	DownloadURI       string                 `json:"downloadUri"`
	Repo              string                 `json:"repo"`
	Path              string                 `json:"path"`
	RemoteURL         string                 `json:"remoteUrl"`
	Created           string                 `json:"created"`
	CreatedBy         string                 `json:"createdBy"`
	LastModified      string                 `json:"lastModified"`
	ModifiedBy        string                 `json:"modifiedBy"`
	LastUpdated       string                 `json:"lastUpdated"`
	Size              string                 `json:"size"`
	MimeType          string                 `json:"mimeType"`
	Checksums         map[string]string      `json:"checksums"`
	OriginalChecksums map[string]string      `json:"originalChecksums"`
	Properties        map[string]interface{} `json:"properties"`
}

// BuildInfo represents build information
type BuildInfo struct {
	Version              string            `json:"version"`
	Name                 string            `json:"name"`
	Number               string            `json:"number"`
	Type                 string            `json:"type"`
	BuildAgent           BuildAgent        `json:"buildAgent"`
	Agent                BuildAgent        `json:"agent"`
	Started              string            `json:"started"`
	DurationMillis       int64             `json:"durationMillis"`
	Principal            string            `json:"principal"`
	ArtifactoryPrincipal string            `json:"artifactoryPrincipal"`
	URL                  string            `json:"url"`
	VCS                  []VCSInfo         `json:"vcs"`
	Modules              []Module          `json:"modules"`
	Governance           Governance        `json:"governance"`
	Issues               Issues            `json:"issues"`
	Properties           map[string]string `json:"properties"`
}

// BuildAgent represents build agent information
type BuildAgent struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// VCSInfo represents version control information
type VCSInfo struct {
	URL      string `json:"url"`
	Revision string `json:"revision"`
	Branch   string `json:"branch"`
	Message  string `json:"message"`
}

// Module represents a build module
type Module struct {
	ID           string             `json:"id"`
	Artifacts    []ModuleArtifact   `json:"artifacts"`
	Dependencies []ModuleDependency `json:"dependencies"`
}

// ModuleArtifact represents a module artifact
type ModuleArtifact struct {
	Type string            `json:"type"`
	SHA1 string            `json:"sha1"`
	MD5  string            `json:"md5"`
	Name string            `json:"name"`
	Path string            `json:"path"`
	Properties map[string]string `json:"properties"`
}

// ModuleDependency represents a module dependency
type ModuleDependency struct {
	Type       string            `json:"type"`
	SHA1       string            `json:"sha1"`
	MD5        string            `json:"md5"`
	ID         string            `json:"id"`
	Scopes     []string          `json:"scopes"`
	Properties map[string]string `json:"properties"`
}

// Governance represents governance information
type Governance struct {
	BlackDuckProperties BlackDuckProperties `json:"blackDuckProperties"`
}

// BlackDuckProperties represents Black Duck scan properties
type BlackDuckProperties struct {
	RunChecks               bool   `json:"runChecks"`
	AppName                 string `json:"appName"`
	AppVersion              string `json:"appVersion"`
	ReportRecipients        string `json:"reportRecipients"`
	ScanMemory              string `json:"scanMemory"`
	ScanTimeout             string `json:"scanTimeout"`
	IncludePublishedArtifacts bool `json:"includePublishedArtifacts"`
	AutoCreateMissingComponentRequests bool `json:"autoCreateMissingComponentRequests"`
	AutoDiscardStaleComponentRequests  bool `json:"autoDiscardStaleComponentRequests"`
}

// Issues represents issues information
type Issues struct {
	Tracker           IssueTracker       `json:"tracker"`
	AggregatedIssues  []AggregatedIssue  `json:"aggregatedIssues"`
	AffectedIssues    []string           `json:"affectedIssues"`
}

// IssueTracker represents issue tracker information
type IssueTracker struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// AggregatedIssue represents an aggregated issue
type AggregatedIssue struct {
	Key        string `json:"key"`
	URL        string `json:"url"`
	Summary    string `json:"summary"`
	Aggregated bool   `json:"aggregated"`
}

// ScanResult represents Xray scan results
type ScanResult struct {
	Summary      ScanSummary    `json:"summary"`
	Alerts       []Alert        `json:"alerts"`
	Licenses     []License      `json:"licenses"`
	Failures     []ScanFailure  `json:"failures"`
	Security     Security       `json:"security"`
	GeneralInfo  GeneralInfo    `json:"general_info"`
}

// ScanSummary represents scan summary
type ScanSummary struct {
	TotalAlerts int    `json:"total_alerts"`
	FailBuild   bool   `json:"fail_build"`
	Message     string `json:"message"`
}

// Alert represents a security or license alert
type Alert struct {
	TopSeverity string      `json:"top_severity"`
	Components  []Component `json:"components"`
}

// Component represents a component with issues
type Component struct {
	ComponentID string  `json:"component_id"`
	Issues      []Issue `json:"issues"`
}

// Issue represents a security or license issue
type Issue struct {
	IssueID       string   `json:"issue_id"`
	Severity      string   `json:"severity"`
	Type          string   `json:"type"`
	Provider      string   `json:"provider"`
	Created       string   `json:"created"`
	Description   string   `json:"description"`
	Summary       string   `json:"summary"`
	CvssV2        string   `json:"cvss_v2"`
	CvssV3        string   `json:"cvss_v3"`
	References    []string `json:"references"`
	FixedVersions []string `json:"fixed_versions"`
}

// ScanFailure represents a scan failure
type ScanFailure struct {
	ComponentID string `json:"component_id"`
	Reason      string `json:"reason"`
}

// License represents license information
type License struct {
	Name       string   `json:"name"`
	Components []string `json:"components"`
	Violation  bool     `json:"violation"`
	Risk       string   `json:"risk"`
}

// Security represents security information
type Security struct {
	Vulnerabilities []Issue `json:"vulnerabilities"`
	Violations      []Issue `json:"violations"`
	Licenses        []License `json:"licenses"`
}

// GeneralInfo represents general scan information
type GeneralInfo struct {
	ComponentID     string `json:"component_id"`
	PackageType     string `json:"package_type"`
	Path            string `json:"path"`
	Name            string `json:"name"`
	Version         string `json:"version"`
	SHA256          string `json:"sha256"`
}
