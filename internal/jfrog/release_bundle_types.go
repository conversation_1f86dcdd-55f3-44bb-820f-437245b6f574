// Package: jfrog
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:jfrog-release-bundle-types:2025-01-27T10:00:00.123Z
//
// Purpose: JFrog Release Bundle V2 data types and structures
// Governance: MCStack v13.5 compliance with enterprise security policies
// Security: Type safety for release bundle operations and distribution
package jfrog

// ReleaseBundleV2 represents a Release Bundle V2
type ReleaseBundleV2 struct {
	Name        string                 `json:"name"`
	Version     string                 `json:"version"`
	Description string                 `json:"description"`
	ReleaseNotes ReleaseNotes          `json:"release_notes"`
	Source      ReleaseBundleSource    `json:"source"`
	Spec        ReleaseBundleSpec      `json:"spec"`
	State       string                 `json:"state"`
	Created     string                 `json:"created"`
	CreatedBy   string                 `json:"created_by"`
	Modified    string                 `json:"modified"`
	ModifiedBy  string                 `json:"modified_by"`
}

// ReleaseNotes represents release notes for a bundle
type ReleaseNotes struct {
	Syntax  string `json:"syntax"`
	Content string `json:"content"`
}

// ReleaseBundleSource represents the source of a release bundle
type ReleaseBundleSource struct {
	Type        string                 `json:"type"`
	Builds      []ReleaseBundleBuild   `json:"builds,omitempty"`
	Artifacts   []ReleaseBundleArtifact `json:"artifacts,omitempty"`
}

// ReleaseBundleBuild represents a build in a release bundle
type ReleaseBundleBuild struct {
	Name    string `json:"name"`
	Number  string `json:"number"`
	Project string `json:"project,omitempty"`
}

// ReleaseBundleArtifact represents an artifact in a release bundle
type ReleaseBundleArtifact struct {
	Path       string            `json:"path"`
	Repository string            `json:"repository"`
	Properties map[string]string `json:"properties,omitempty"`
}

// ReleaseBundleSpec represents the specification for a release bundle
type ReleaseBundleSpec struct {
	Queries []ReleaseBundleQuery `json:"queries"`
}

// ReleaseBundleQuery represents a query for selecting artifacts
type ReleaseBundleQuery struct {
	AQL         string            `json:"aql,omitempty"`
	QueryName   string            `json:"query_name,omitempty"`
	Mappings    []ArtifactMapping `json:"mappings,omitempty"`
	AddedProps  []Property        `json:"added_props,omitempty"`
}

// ArtifactMapping represents artifact path mapping
type ArtifactMapping struct {
	Input  string `json:"input"`
	Output string `json:"output"`
}

// Property represents a property key-value pair
type Property struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// DistributionTarget represents a distribution target
type DistributionTarget struct {
	Name        string `json:"name"`
	ServiceID   string `json:"service_id"`
	SiteName    string `json:"site_name"`
	CityName    string `json:"city_name"`
	CountryCode string `json:"country_code"`
}

// TransferSettings represents file transfer settings
type TransferSettings struct {
	SourceRepo      string `json:"source_repo"`
	TargetRepo      string `json:"target_repo"`
	SourceServer    string `json:"source_server,omitempty"`
	TargetServer    string `json:"target_server,omitempty"`
	IncludePatterns []string `json:"include_patterns,omitempty"`
	ExcludePatterns []string `json:"exclude_patterns,omitempty"`
	Properties      map[string]string `json:"properties,omitempty"`
}
