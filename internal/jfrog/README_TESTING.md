# JFrog Client Testing Guide

## Overview

This document provides comprehensive testing guidance for the JFrog client implementation in the MC Poly Installer project. The testing suite includes unit tests, integration tests, and end-to-end validation to ensure robust and reliable JFrog integration.

## Test Architecture

### Test Levels

1. **Unit Tests** (`client_unit_test.go`)
   - Mock-based testing of individual methods
   - No external dependencies required
   - Fast execution and reliable results
   - Tests business logic and error handling

2. **Integration Tests** (`integration_test.go`)
   - Tests with real JFrog CLI commands
   - Requires JFrog CLI installation
   - Tests command construction and execution
   - Validates CLI integration patterns

3. **End-to-End Tests** (`e2e_test.go`)
   - Complete workflow validation
   - Tests full system integration
   - Requires JFrog CLI and server access
   - Validates real-world scenarios

## Prerequisites

### Required Software

1. **Go 1.21+**
   ```bash
   go version
   ```

2. **JFrog CLI** (for integration and E2E tests)
   ```bash
   # Install JFrog CLI
   curl -fL https://getcli.jfrog.io | sh
   sudo mv jf /usr/local/bin/
   
   # Verify installation
   jf --version
   ```

3. **Test Dependencies**
   ```bash
   go mod download
   go install github.com/onsi/ginkgo/v2/ginkgo@latest
   go install github.com/onsi/gomega/...@latest
   ```

### Environment Setup

#### For Integration Tests
```bash
export JFROG_URL="https://your-instance.jfrog.io"
export JFROG_USERNAME="your-username"
export JFROG_PASSWORD="your-password"
```

#### For E2E Tests
```bash
export RUN_E2E_TESTS=1
export JFROG_URL="https://your-instance.jfrog.io"
export JFROG_USERNAME="your-username"
export JFROG_PASSWORD="your-password"
```

## Running Tests

### Quick Test Commands

```bash
# Run all unit tests
go test -v ./internal/jfrog/... -tags=unit

# Run integration tests (requires JFrog CLI)
go test -v ./internal/jfrog/... -tags=integration

# Run E2E tests (requires JFrog CLI and server)
go test -v ./internal/jfrog/... -tags=e2e

# Run all tests with coverage
go test -v -cover ./internal/jfrog/...

# Run tests with race detection
go test -v -race ./internal/jfrog/...
```

### Comprehensive Test Runner

Use the built-in test runner for complete validation:

```bash
# Run comprehensive test suite
go run -tags=test_runner ./internal/jfrog/test_runner.go
```

### Test Categories

#### Unit Tests (Always Available)
```bash
# Test specific functionality
go test -v -run TestClient_GetArtifactInfo ./internal/jfrog/
go test -v -run TestClient_Scan ./internal/jfrog/
go test -v -run TestClient_BuildOperations ./internal/jfrog/
```

#### Integration Tests (Requires JFrog CLI)
```bash
# Test CLI integration
go test -v -tags=integration -run TestJFrogCLIAvailability ./internal/jfrog/
go test -v -tags=integration -run TestClientCreation ./internal/jfrog/
```

#### E2E Tests (Requires JFrog CLI + Server)
```bash
# Test complete workflows
RUN_E2E_TESTS=1 go test -v -tags=e2e -run TestCompleteWorkflow ./internal/jfrog/
```

## Test Configuration

### Mock Configuration
Unit tests use mock configurations that don't require real JFrog services:

```go
config := &config.Config{
    Environments: config.Environments{
        Online: config.Environment{
            JFrog: config.JFrogConfig{
                URL:      "https://test.jfrog.io",
                Username: "testuser",
                Password: "testpass",
            },
        },
    },
}
```

### Real Configuration
Integration and E2E tests can use real JFrog instances:

```yaml
environments:
  online:
    jfrog:
      url: "https://your-instance.jfrog.io"
      username: "your-username"
      password: "your-password"
```

## Test Examples

### Unit Test Example
```go
func TestClient_Upload(t *testing.T) {
    testClient := setupMockClient(t)
    ctx := context.Background()

    err := testClient.Upload(ctx, "test.jar", "libs-release/", map[string]interface{}{
        "threads": 4,
        "build_name": "test-build",
    })
    
    // Test validates method structure and parameter handling
    assert.NotNil(t, err) // Expected without real JFrog instance
}
```

### Integration Test Example
```go
func TestJFrogCLIIntegration(t *testing.T) {
    if _, err := exec.LookPath("jf"); err != nil {
        t.Skip("JFrog CLI not available")
    }

    client, err := NewClient(testConfig)
    if err != nil {
        t.Logf("Expected error without real server: %v", err)
        return
    }

    err = client.Health(context.Background())
    // Test validates CLI command execution
}
```

### E2E Test Example
```go
func TestCompleteWorkflow(t *testing.T) {
    // 1. Load configuration
    cfg := loadTestConfig()
    
    // 2. Create client
    client, err := NewClient(cfg)
    require.NoError(t, err)
    
    // 3. Test artifact lifecycle
    testArtifactUploadDownload(t, client)
    
    // 4. Test build operations
    testBuildPublishPromote(t, client)
    
    // 5. Test security scanning
    testSecurityScanning(t, client)
}
```

## Troubleshooting

### Common Issues

#### JFrog CLI Not Found
```
Error: exec: "jf": executable file not found in $PATH
```
**Solution:** Install JFrog CLI following the prerequisites section.

#### Connection Refused
```
Error: connection refused
```
**Solution:** Verify JFrog server URL and network connectivity.

#### Authentication Failed
```
Error: 401 Unauthorized
```
**Solution:** Check username/password or use access tokens.

#### Permission Denied
```
Error: 403 Forbidden
```
**Solution:** Verify user permissions for repository operations.

### Debug Mode

Enable debug logging for detailed test output:

```bash
export LOG_LEVEL=debug
go test -v ./internal/jfrog/...
```

### Test Isolation

Tests are designed to be isolated and can run in parallel:

```bash
go test -v -parallel 4 ./internal/jfrog/...
```

## Continuous Integration

### GitHub Actions Example
```yaml
name: JFrog Client Tests
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      - run: go test -v -tags=unit ./internal/jfrog/...

  integration-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      - name: Install JFrog CLI
        run: |
          curl -fL https://getcli.jfrog.io | sh
          sudo mv jf /usr/local/bin/
      - run: go test -v -tags=integration ./internal/jfrog/...
```

## Test Coverage

Generate and view test coverage:

```bash
# Generate coverage report
go test -coverprofile=coverage.out ./internal/jfrog/...

# View coverage in browser
go tool cover -html=coverage.out

# View coverage summary
go tool cover -func=coverage.out
```

## Best Practices

1. **Test Isolation**: Each test should be independent and not rely on external state
2. **Mock External Dependencies**: Use mocks for external services in unit tests
3. **Clear Test Names**: Use descriptive test names that explain what is being tested
4. **Proper Cleanup**: Always clean up resources created during tests
5. **Error Testing**: Test both success and failure scenarios
6. **Documentation**: Document complex test scenarios and setup requirements

## Contributing

When adding new functionality to the JFrog client:

1. Add corresponding unit tests with mocks
2. Add integration tests if CLI interaction is involved
3. Add E2E tests for complete workflows
4. Update this documentation with new test scenarios
5. Ensure all tests pass before submitting PR

## Support

For testing issues or questions:

1. Check the troubleshooting section above
2. Review test logs for specific error messages
3. Verify prerequisites are met
4. Check JFrog CLI and server connectivity
5. Consult JFrog documentation for service-specific issues
