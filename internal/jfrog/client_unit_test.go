// Package: jfrog
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:jfrog-unit-test:2025-01-27T10:00:00.123Z
//
// Purpose: Comprehensive unit tests with mocking for JFrog client
// Governance: MCStack v13.5 compliance with isolated testing
// Security: Mock external dependencies for secure testing
package jfrog

import (
	"context"
	"testing"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockCommand represents a mock command execution
type MockCommand struct {
	Name     string
	Args     []string
	Output   string
	Error    error
	ExitCode int
}

// CommandExecutor interface for dependency injection
type CommandExecutor interface {
	ExecuteCommand(ctx context.Context, name string, args ...string) ([]byte, error)
}

// MockCommandExecutor implements CommandExecutor for testing
type MockCommandExecutor struct {
	Commands []MockCommand
	CallLog  []MockCommand
}

func (m *MockCommandExecutor) ExecuteCommand(ctx context.Context, name string, args ...string) ([]byte, error) {
	// Log the call
	call := MockCommand{Name: name, Args: args}
	m.CallLog = append(m.CallLog, call)

	// Find matching mock command
	for _, cmd := range m.Commands {
		if cmd.Name == name && m.argsMatch(cmd.Args, args) {
			if cmd.Error != nil {
				return nil, cmd.Error
			}
			return []byte(cmd.Output), nil
		}
	}

	// Default response for unmocked commands
	return []byte("mocked response"), nil
}

func (m *MockCommandExecutor) argsMatch(expected, actual []string) bool {
	if len(expected) == 0 {
		return true // Match any args if none specified
	}

	if len(expected) != len(actual) {
		return false
	}

	for i, exp := range expected {
		if exp != actual[i] && exp != "*" { // "*" matches any argument
			return false
		}
	}
	return true
}

// TestableClient wraps Client with mock executor
type TestableClient struct {
	*Client
	mockExecutor *MockCommandExecutor
}

func setupMockClient(t *testing.T) *TestableClient {
	cfg := &config.Config{
		Environments: config.EnvironmentsConfig{
			Online: config.OnlineEnvironment{
				JFrog: config.JFrogConfig{
					URL:      "https://test.jfrog.io",
					Username: "testuser",
					Password: "testpass",
				},
			},
		},
	}

	// Disable logging during tests
	logrus.SetLevel(logrus.FatalLevel)

	client := &Client{
		config: cfg,
	}

	mockExecutor := &MockCommandExecutor{
		Commands: []MockCommand{
			{Name: "jf", Args: []string{"--version"}, Output: "jfrog version 2.52.0"},
			{Name: "jf", Args: []string{"rt", "ping"}, Output: "OK"},
		},
	}

	return &TestableClient{
		Client:       client,
		mockExecutor: mockExecutor,
	}
}

func TestClient_GetArtifactInfo(t *testing.T) {
	testClient := setupMockClient(t)
	ctx := context.Background()

	tests := []struct {
		name     string
		path     string
		expected func(*ArtifactInfo)
	}{
		{
			name: "valid artifact path",
			path: "libs-release/com/example/test/1.0.0/test.jar",
			expected: func(info *ArtifactInfo) {
				assert.Contains(t, info.URI, "libs-release/com/example/test/1.0.0/test.jar")
				assert.Equal(t, "libs-release", info.Repo)
				assert.Contains(t, info.Checksums, "sha1")
				assert.Contains(t, info.Checksums, "sha256")
				assert.Contains(t, info.Checksums, "md5")
			},
		},
		{
			name: "nested path",
			path: "docker-local/my-app/1.0.0/manifest.json",
			expected: func(info *ArtifactInfo) {
				assert.Contains(t, info.URI, "docker-local/my-app/1.0.0/manifest.json")
				assert.Equal(t, "docker-local", info.Repo)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			info, err := testClient.GetArtifactInfo(ctx, tt.path)

			require.NoError(t, err)
			require.NotNil(t, info)
			tt.expected(info)
		})
	}
}

func TestClient_GetBuildInfo(t *testing.T) {
	testClient := setupMockClient(t)
	ctx := context.Background()

	tests := []struct {
		name        string
		buildName   string
		buildNumber string
		expected    func(*BuildInfo)
	}{
		{
			name:        "valid build info",
			buildName:   "test-build",
			buildNumber: "1.0.0",
			expected: func(info *BuildInfo) {
				assert.Equal(t, "test-build", info.Name)
				assert.Equal(t, "1.0.0", info.Number)
				assert.NotEmpty(t, info.Started)
				assert.NotNil(t, info.VCS)
				assert.NotNil(t, info.Modules)
			},
		},
		{
			name:        "snapshot build",
			buildName:   "snapshot-build",
			buildNumber: "1.0.0-SNAPSHOT",
			expected: func(info *BuildInfo) {
				assert.Equal(t, "snapshot-build", info.Name)
				assert.Equal(t, "1.0.0-SNAPSHOT", info.Number)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			info, err := testClient.GetBuildInfo(ctx, tt.buildName, tt.buildNumber)

			require.NoError(t, err)
			require.NotNil(t, info)
			tt.expected(info)
		})
	}
}

func TestClient_Scan(t *testing.T) {
	testClient := setupMockClient(t)
	ctx := context.Background()

	tests := []struct {
		name     string
		target   string
		expected func(*ScanResult)
	}{
		{
			name:   "repository scan",
			target: "libs-release-local",
			expected: func(result *ScanResult) {
				assert.GreaterOrEqual(t, result.Summary.TotalAlerts, 0)
				assert.NotNil(t, result.Alerts)
				assert.NotNil(t, result.Licenses)
			},
		},
		{
			name:   "build scan",
			target: "build://test-build/1.0.0",
			expected: func(result *ScanResult) {
				assert.GreaterOrEqual(t, result.Summary.TotalAlerts, 0)
				assert.NotEmpty(t, result.Summary.Message)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := testClient.Scan(ctx, tt.target)

			require.NoError(t, err)
			require.NotNil(t, result)
			tt.expected(result)
		})
	}
}

func TestClient_ListRepositories(t *testing.T) {
	testClient := setupMockClient(t)
	ctx := context.Background()

	repos, err := testClient.ListRepositories(ctx)

	require.NoError(t, err)
	require.NotNil(t, repos)
	assert.Greater(t, len(repos), 0)

	// Verify repository structure
	for _, repo := range repos {
		assert.NotEmpty(t, repo.Key)
		assert.NotEmpty(t, repo.Type)
		assert.NotEmpty(t, repo.PackageType)
	}

	// Check for expected repository types
	repoTypes := make(map[string]bool)
	for _, repo := range repos {
		repoTypes[repo.Type] = true
	}

	assert.True(t, repoTypes["LOCAL"] || repoTypes["REMOTE"] || repoTypes["VIRTUAL"])
}

func TestClient_GetStorageInfo(t *testing.T) {
	testClient := setupMockClient(t)
	ctx := context.Background()

	storageInfo, err := testClient.GetStorageInfo(ctx)

	require.NoError(t, err)
	require.NotNil(t, storageInfo)

	// Verify required fields
	expectedFields := []string{
		"totalSpace", "usedSpace", "freeSpace",
		"repositoryCount", "artifactCount",
		"fileStoreType", "fileStorePath",
	}

	for _, field := range expectedFields {
		assert.Contains(t, storageInfo, field, "Missing field: %s", field)
	}
}

func TestClient_GetUsers(t *testing.T) {
	testClient := setupMockClient(t)
	ctx := context.Background()

	users, err := testClient.GetUsers(ctx)

	require.NoError(t, err)
	require.NotNil(t, users)
	assert.Greater(t, len(users), 0)

	// Verify user structure
	for _, user := range users {
		assert.Contains(t, user, "name")
		assert.Contains(t, user, "email")
		assert.Contains(t, user, "admin")
		assert.Contains(t, user, "realm")
		assert.Contains(t, user, "groups")
	}
}

func TestClient_ExecuteAQL(t *testing.T) {
	testClient := setupMockClient(t)
	ctx := context.Background()

	query := `items.find({"repo":"libs-release","type":"file"}).include("name","repo","path","size")`

	results, err := testClient.ExecuteAQL(ctx, query)

	require.NoError(t, err)
	require.NotNil(t, results)
	assert.Greater(t, len(results), 0)

	// Verify result structure
	for _, result := range results {
		assert.Contains(t, result, "repo")
		assert.Contains(t, result, "name")
		assert.Contains(t, result, "path")
		assert.Contains(t, result, "type")
	}
}

func TestClient_GetLicenseInfo(t *testing.T) {
	testClient := setupMockClient(t)
	ctx := context.Background()

	licenseInfo, err := testClient.GetLicenseInfo(ctx)

	require.NoError(t, err)
	require.NotNil(t, licenseInfo)

	// Verify license info structure
	expectedFields := []string{
		"type", "validThrough", "licensedTo",
		"licenseHash", "nodeId", "nodeUrl", "features",
	}

	for _, field := range expectedFields {
		assert.Contains(t, licenseInfo, field, "Missing field: %s", field)
	}

	// Verify features is a slice
	features, ok := licenseInfo["features"].([]string)
	assert.True(t, ok, "Features should be a string slice")
	assert.Greater(t, len(features), 0, "Should have at least one feature")
}

func TestClient_GetPermissionTargets(t *testing.T) {
	testClient := setupMockClient(t)
	ctx := context.Background()

	permissionTargets, err := testClient.GetPermissionTargets(ctx)

	require.NoError(t, err)
	require.NotNil(t, permissionTargets)
	assert.Greater(t, len(permissionTargets), 0)

	// Verify permission target structure
	for _, target := range permissionTargets {
		assert.Contains(t, target, "name")
		assert.Contains(t, target, "repositories")
		assert.Contains(t, target, "principals")

		// Verify principals structure
		principals, ok := target["principals"].(map[string]interface{})
		assert.True(t, ok, "Principals should be a map")
		assert.Contains(t, principals, "users")
		assert.Contains(t, principals, "groups")
	}
}
