// Package: jfrog
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:jfrog-client-main:2025-01-27T10:00:00.123Z
//
// Purpose: JFrog Artifactory and Xray integration - main client file (refactored)
// Governance: MCStack v13.5 compliance with enterprise security policies
// Security: Artifact signing, vulnerability scanning, and supply chain security
//
// REFACTORING NOTE:
// This file has been refactored for better maintainability from a 2,200+ line monolith
// into a modular architecture with specialized operation files:
//
// Core Architecture:
// - types.go: Data structures and type definitions
// - release_bundle_types.go: Release Bundle V2 specific types
// - client_core.go: Core client operations (initialization, health, validation)
// - artifact_operations.go: Artifact management (upload, download, search, copy, move, delete)
// - build_operations.go: Build lifecycle (collect, publish, promote, discard, scan)
// - release_bundle_operations.go: Release Bundle V2 operations (create, distribute, abort, annotate)
// - package_operations.go: Package management (Docker, NPM, Go, Python, Maven, Gradle)
// - repository_operations.go: Repository and user management (CRUD operations, permissions)
// - security_operations.go: Security scanning and vulnerability management (Xray integration)
//
// This main file now serves as the entry point and contains only essential
// imports and documentation. All functionality is distributed across the
// specialized operation files for better maintainability, testing, and code organization.
package jfrog

// The JFrog client implementation is now distributed across multiple files
// for better maintainability and separation of concerns:
//
// 1. TYPES & STRUCTURES (types.go, release_bundle_types.go)
//    - Client struct and configuration
//    - ArtifactInfo, BuildInfo, ScanResult structures
//    - ReleaseBundleV2, TransferSettings structures
//    - All data models for JFrog operations
//
// 2. CORE OPERATIONS (client_core.go)
//    - NewClient() - Client initialization and configuration
//    - Health() - Service health checks
//    - ValidateConnection() - Connection validation
//    - GetVersion() - Version information
//    - GetSystemInfo() - System information
//    - CreateAccessToken() - Token management
//    - Cleanup() - Resource cleanup
//
// 3. ARTIFACT MANAGEMENT (artifact_operations.go)
//    - Upload() - Upload artifacts to repositories
//    - Download() - Download artifacts from repositories
//    - Search() - Search for artifacts
//    - GetArtifactInfo() - Get detailed artifact information
//    - Copy() - Copy artifacts within Artifactory
//    - Move() - Move artifacts within Artifactory
//    - Delete() - Delete artifacts from repositories
//    - SetProperties() - Set artifact properties
//    - DeleteProperties() - Delete artifact properties
//
// 4. BUILD LIFECYCLE (build_operations.go)
//    - BuildCollectEnv() - Collect environment variables
//    - BuildAddGit() - Add Git information to builds
//    - BuildPublish() - Publish build information
//    - GetBuildInfo() - Retrieve build information
//    - BuildPromote() - Promote builds between repositories
//    - BuildDiscard() - Discard old builds
//    - BuildScan() - Scan builds with Xray
//    - CreateReleaseBundle() - Create release bundles
//    - DistributeReleaseBundle() - Distribute release bundles
//
// 5. RELEASE BUNDLE V2 (release_bundle_operations.go)
//    - CreateReleaseBundleV2() - Create Release Bundle V2
//    - DistributeReleaseBundleV2() - Distribute Release Bundle V2
//    - AbortReleaseBundleV2() - Abort distribution
//    - AnnotateReleaseBundleV2() - Add annotations
//    - CreateReleaseBundleFromBuilds() - Create from builds
//    - GetReleaseBundleV2() - Get bundle information
//    - TransferFiles() - Transfer files between servers
//    - DownloadFiles() - Advanced download operations
//
// 6. PACKAGE MANAGEMENT (package_operations.go)
//    - DockerPush() / DockerPull() - Docker image operations
//    - NpmPublish() / NpmInstall() - NPM package operations
//    - GoPublish() - Go module publishing
//    - PipInstall() - Python package installation
//    - MavenDeploy() - Maven artifact deployment
//    - GradleDeploy() - Gradle artifact deployment
//
// 7. REPOSITORY & USER MANAGEMENT (repository_operations.go)
//    - ListRepositories() - List all repositories
//    - CreateRepository() - Create new repositories
//    - UpdateRepository() - Update repository configuration
//    - DeleteRepository() - Delete repositories
//    - GetStorageInfo() - Get storage information
//    - GetUsers() - List users
//    - CreateUser() - Create new users
//    - GetPermissionTargets() - Get permission configurations
//    - ExecuteAQL() - Execute AQL queries
//    - GetLicenseInfo() - Get license information
//
// 8. SECURITY OPERATIONS (security_operations.go)
//    - Scan() - Perform Xray security scans
//    - ScanBuild() - Scan specific builds
//    - CreateWatch() - Create Xray watches
//    - CreatePolicy() - Create security policies
//    - GetVulnerabilities() - Get vulnerability information
//    - GetLicenseViolations() - Get license violations
//    - IgnoreVulnerability() - Create ignore rules
//
// BENEFITS OF REFACTORING:
// ✅ Improved Maintainability - Each file has a single responsibility
// ✅ Better Testing - Easier to write focused unit tests
// ✅ Enhanced Readability - Logical grouping of related functionality
// ✅ Reduced Complexity - Smaller, more manageable files
// ✅ Easier Navigation - Developers can quickly find relevant code
// ✅ Better Documentation - Each file can have specialized documentation
// ✅ Simplified Debugging - Issues can be isolated to specific operation areas
// ✅ Enhanced Collaboration - Multiple developers can work on different areas
//
// USAGE EXAMPLE:
//
//   import "github.com/mchorfa/mc-poly-installer/internal/jfrog"
//
//   // Create client (defined in client_core.go)
//   client, err := jfrog.NewClient(config)
//   if err != nil {
//       return err
//   }
//
//   // Upload artifact (defined in artifact_operations.go)
//   err = client.Upload(ctx, "artifact.jar", "libs-release/", options)
//
//   // Scan for vulnerabilities (defined in security_operations.go)
//   result, err := client.Scan(ctx, "libs-release")
//
//   // Create release bundle (defined in release_bundle_operations.go)
//   err = client.CreateReleaseBundleV2(ctx, "my-bundle", "1.0.0", options)
//
// All methods maintain the same signatures and behavior as before,
// but are now organized in a more maintainable structure.
