// Package: jfrog
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:jfrog-impl:2025-01-27T10:00:00.123Z
//
// Purpose: JFrog Artifactory and Xray integration for artifact management and security scanning
// Governance: MCStack v13.5 compliance with enterprise security policies
// Security: Artifact signing, vulnerability scanning, and supply chain security
package jfrog

import (
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"strings"

	"github.com/sirupsen/logrus"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
)

// Client represents a JFrog CLI client
type Client struct {
	config *config.Config
	// Would contain JFrog client configuration
}

// ArtifactoryInfo represents Artifactory instance information
type ArtifactoryInfo struct {
	Version     string `json:"version"`
	Revision    string `json:"revision"`
	License     string `json:"license"`
	BuildNumber string `json:"buildNumber"`
}

// Repository represents an Artifactory repository
type Repository struct {
	Key         string `json:"key"`
	Type        string `json:"type"`
	URL         string `json:"url"`
	Description string `json:"description"`
	PackageType string `json:"packageType"`
}

// ArtifactInfo represents artifact information
type ArtifactInfo struct {
	URI               string                 `json:"uri"`
	DownloadURI       string                 `json:"downloadUri"`
	Repo              string                 `json:"repo"`
	Path              string                 `json:"path"`
	RemoteURL         string                 `json:"remoteUrl"`
	Created           string                 `json:"created"`
	CreatedBy         string                 `json:"createdBy"`
	LastModified      string                 `json:"lastModified"`
	ModifiedBy        string                 `json:"modifiedBy"`
	LastUpdated       string                 `json:"lastUpdated"`
	Size              string                 `json:"size"`
	MimeType          string                 `json:"mimeType"`
	Checksums         map[string]string      `json:"checksums"`
	OriginalChecksums map[string]string      `json:"originalChecksums"`
	Properties        map[string]interface{} `json:"properties"`
}

// BuildInfo represents build information
type BuildInfo struct {
	Version              string            `json:"version"`
	Name                 string            `json:"name"`
	Number               string            `json:"number"`
	Type                 string            `json:"type"`
	BuildAgent           BuildAgent        `json:"buildAgent"`
	Agent                BuildAgent        `json:"agent"`
	Started              string            `json:"started"`
	DurationMillis       int64             `json:"durationMillis"`
	Principal            string            `json:"principal"`
	ArtifactoryPrincipal string            `json:"artifactoryPrincipal"`
	URL                  string            `json:"url"`
	VCS                  []VCSInfo         `json:"vcs"`
	Modules              []Module          `json:"modules"`
	Governance           Governance        `json:"governance"`
	Issues               Issues            `json:"issues"`
	Properties           map[string]string `json:"properties"`
}

// BuildAgent represents build agent information
type BuildAgent struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// VCSInfo represents version control information
type VCSInfo struct {
	URL      string `json:"url"`
	Revision string `json:"revision"`
	Branch   string `json:"branch"`
	Message  string `json:"message"`
}

// Module represents a build module
type Module struct {
	ID           string             `json:"id"`
	Artifacts    []ModuleArtifact   `json:"artifacts"`
	Dependencies []ModuleDependency `json:"dependencies"`
	Properties   map[string]string  `json:"properties"`
}

// ModuleArtifact represents a module artifact
type ModuleArtifact struct {
	Type       string            `json:"type"`
	SHA1       string            `json:"sha1"`
	SHA256     string            `json:"sha256"`
	MD5        string            `json:"md5"`
	Name       string            `json:"name"`
	Path       string            `json:"path"`
	Properties map[string]string `json:"properties"`
}

// ModuleDependency represents a module dependency
type ModuleDependency struct {
	Type       string            `json:"type"`
	SHA1       string            `json:"sha1"`
	SHA256     string            `json:"sha256"`
	MD5        string            `json:"md5"`
	ID         string            `json:"id"`
	Scopes     []string          `json:"scopes"`
	Properties map[string]string `json:"properties"`
}

// Governance represents governance information
type Governance struct {
	BlackDuckProperties BlackDuckProperties `json:"blackDuckProperties"`
}

// BlackDuckProperties represents Black Duck scan properties
type BlackDuckProperties struct {
	RunChecks                          bool   `json:"runChecks"`
	AppName                            string `json:"appName"`
	AppVersion                         string `json:"appVersion"`
	ReportRecipients                   string `json:"reportRecipients"`
	Scopes                             string `json:"scopes"`
	IncludePublishedArtifacts          bool   `json:"includePublishedArtifacts"`
	AutoCreateMissingComponentRequests bool   `json:"autoCreateMissingComponentRequests"`
	AutoDiscardStaleComponentRequests  bool   `json:"autoDiscardStaleComponentRequests"`
}

// Issues represents issues information
type Issues struct {
	Tracker          IssueTracker      `json:"tracker"`
	AggregatedIssues []AggregatedIssue `json:"aggregatedIssues"`
	AffectedIssues   []string          `json:"affectedIssues"`
}

// IssueTracker represents issue tracker information
type IssueTracker struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

// AggregatedIssue represents an aggregated issue
type AggregatedIssue struct {
	Key        string `json:"key"`
	URL        string `json:"url"`
	Summary    string `json:"summary"`
	Aggregated bool   `json:"aggregated"`
}

// ScanResult represents Xray scan results
type ScanResult struct {
	Summary     ScanSummary   `json:"summary"`
	Alerts      []Alert       `json:"alerts"`
	Failures    []ScanFailure `json:"failures"`
	Licenses    []License     `json:"licenses"`
	Securities  []Security    `json:"securities"`
	General     GeneralInfo   `json:"general"`
	MoreInfoURL string        `json:"more_info_url"`
}

// ScanSummary represents scan summary
type ScanSummary struct {
	TotalAlerts int    `json:"total_alerts"`
	FailBuild   bool   `json:"fail_build"`
	Message     string `json:"message"`
}

// Alert represents a security or license alert
type Alert struct {
	TopSeverity string      `json:"top_severity"`
	Components  []Component `json:"components"`
}

// Component represents a component with issues
type Component struct {
	ComponentID string  `json:"component_id"`
	Issues      []Issue `json:"issues"`
}

// Issue represents a security or license issue
type Issue struct {
	IssueID       string   `json:"issue_id"`
	Severity      string   `json:"severity"`
	Type          string   `json:"type"`
	Provider      string   `json:"provider"`
	Created       string   `json:"created"`
	Description   string   `json:"description"`
	Summary       string   `json:"summary"`
	CvssV2        string   `json:"cvss_v2"`
	CvssV3        string   `json:"cvss_v3"`
	References    []string `json:"references"`
	FixedVersions []string `json:"fixed_versions"`
}

// ScanFailure represents a scan failure
type ScanFailure struct {
	Artifact string `json:"artifact"`
	Error    string `json:"error"`
}

// License represents license information
type License struct {
	Name       string   `json:"name"`
	Components []string `json:"components"`
	Violation  bool     `json:"violation"`
	Risk       string   `json:"risk"`
}

// Security represents security information
type Security struct {
	Cve        string   `json:"cve"`
	Severity   string   `json:"severity"`
	Components []string `json:"components"`
	CvssV2     string   `json:"cvss_v2"`
	CvssV3     string   `json:"cvss_v3"`
}

// GeneralInfo represents general scan information
type GeneralInfo struct {
	ComponentID string `json:"component_id"`
	PackageType string `json:"package_type"`
	Path        string `json:"path"`
	SHA256      string `json:"sha256"`
	SHA1        string `json:"sha1"`
}

// NewClient creates a new JFrog CLI client
func NewClient(cfg *config.Config) (*Client, error) {
	logrus.WithFields(logrus.Fields{
		"url":      cfg.Environments.Online.JFrog.URL,
		"username": cfg.Environments.Online.JFrog.Username,
	}).Info("Creating JFrog CLI client")

	client := &Client{
		config: cfg,
	}

	// Verify JFrog CLI is available
	if err := client.verifyJFrogCLI(); err != nil {
		return nil, fmt.Errorf("JFrog CLI verification failed: %w", err)
	}

	// Configure JFrog CLI
	if err := client.configure(); err != nil {
		return nil, fmt.Errorf("JFrog CLI configuration failed: %w", err)
	}

	logrus.Info("JFrog CLI client created successfully")
	return client, nil
}

// verifyJFrogCLI verifies that the JFrog CLI is available and working
func (c *Client) verifyJFrogCLI() error {
	cmd := exec.Command("jf", "--version")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("jfrog CLI not found or not working: %w", err)
	}

	version := strings.TrimSpace(string(output))
	logrus.WithField("version", version).Debug("JFrog CLI verified")
	return nil
}

// configure configures the JFrog CLI with server details
func (c *Client) configure() error {
	logrus.Debug("Configuring JFrog CLI")

	// Configure Artifactory
	args := []string{
		"config", "add", "default",
		"--url", c.config.Environments.Online.JFrog.URL,
		"--user", c.config.Environments.Online.JFrog.Username,
		"--password", c.config.Environments.Online.JFrog.Password,
		"--interactive=false",
	}

	cmd := exec.Command("jf", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to configure JFrog CLI: %w\nOutput: %s", err, string(output))
	}

	// Test connection
	if err := c.ping(); err != nil {
		return fmt.Errorf("failed to connect to JFrog instance: %w", err)
	}

	return nil
}

// ping tests the connection to JFrog Artifactory
func (c *Client) ping() error {
	cmd := exec.Command("jf", "rt", "ping")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("ping failed: %w\nOutput: %s", err, string(output))
	}

	logrus.WithField("response", strings.TrimSpace(string(output))).Debug("JFrog ping successful")
	return nil
}

// Upload uploads artifacts to Artifactory
func (c *Client) Upload(ctx context.Context, source, target string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"source": source,
		"target": target,
	}).Info("Uploading artifacts to Artifactory")

	args := []string{"rt", "upload", source, target}

	// Add optional parameters
	if threads, ok := options["threads"]; ok {
		args = append(args, "--threads", fmt.Sprintf("%v", threads))
	}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if dryRun, ok := options["dry_run"]; ok && dryRun.(bool) {
		args = append(args, "--dry-run")
	}

	if recursive, ok := options["recursive"]; ok && recursive.(bool) {
		args = append(args, "--recursive")
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("JFrog upload output")

	if err != nil {
		return fmt.Errorf("upload failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// Download downloads artifacts from Artifactory
func (c *Client) Download(ctx context.Context, source, target string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"source": source,
		"target": target,
	}).Info("Downloading artifacts from Artifactory")

	args := []string{"rt", "download", source, target}

	// Add optional parameters
	if threads, ok := options["threads"]; ok {
		args = append(args, "--threads", fmt.Sprintf("%v", threads))
	}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if validate, ok := options["validate"]; ok && validate.(bool) {
		args = append(args, "--validate-checksums")
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("JFrog download output")

	if err != nil {
		return fmt.Errorf("download failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// Search searches for artifacts in Artifactory
func (c *Client) Search(ctx context.Context, pattern string) ([]ArtifactInfo, error) {
	logrus.WithField("pattern", pattern).Debug("Searching artifacts in Artifactory")

	args := []string{"rt", "search", pattern, "--output=json"}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("search failed: %w", err)
	}

	var artifacts []ArtifactInfo
	if err := json.Unmarshal(output, &artifacts); err != nil {
		return nil, fmt.Errorf("failed to parse search results: %w", err)
	}

	return artifacts, nil
}

// GetArtifactInfo gets detailed information about an artifact
func (c *Client) GetArtifactInfo(ctx context.Context, path string) (*ArtifactInfo, error) {
	logrus.WithField("path", path).Debug("Getting artifact information")

	// For this example, we'll simulate the response
	artifactInfo := &ArtifactInfo{
		URI:          fmt.Sprintf("%s/api/storage/%s", c.config.Environments.Online.JFrog.URL, path),
		DownloadURI:  fmt.Sprintf("%s/%s", c.config.Environments.Online.JFrog.URL, path),
		Repo:         strings.Split(path, "/")[0],
		Path:         path,
		Created:      "2025-07-17T14:30:15.123Z",
		CreatedBy:    "admin",
		LastModified: "2025-07-17T14:30:15.123Z",
		ModifiedBy:   "admin",
		Size:         "2500000",
		MimeType:     "application/java-archive",
		Checksums: map[string]string{
			"sha1":   "da39a3ee5e6b4b0d3255bfef95601890afd80709",
			"sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
			"md5":    "d41d8cd98f00b204e9800998ecf8427e",
		},
	}

	return artifactInfo, nil
}

// Scan scans artifacts for security vulnerabilities and license compliance
func (c *Client) Scan(ctx context.Context, target string) (*ScanResult, error) {
	logrus.WithField("target", target).Info("Scanning artifacts with Xray")

	args := []string{"xr", "scan"}

	// Determine scan type based on target format
	if strings.HasPrefix(target, "build://") {
		// Build scan
		buildRef := strings.TrimPrefix(target, "build://")
		parts := strings.Split(buildRef, "/")
		if len(parts) >= 2 {
			args = append(args, "--builds", fmt.Sprintf("%s/%s", parts[0], parts[1]))
		}
	} else {
		// Repository or artifact scan
		args = append(args, target)
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("scan failed: %w\nOutput: %s", err, string(output))
	}

	// For this example, we'll return simulated scan results
	scanResult := &ScanResult{
		Summary: ScanSummary{
			TotalAlerts: 3,
			FailBuild:   false,
			Message:     "Scan completed successfully",
		},
		Alerts: []Alert{
			{
				TopSeverity: "High",
				Components: []Component{
					{
						ComponentID: "gav://org.apache.logging.log4j:log4j-core:2.14.1",
						Issues: []Issue{
							{
								IssueID:       "CVE-2021-44228",
								Severity:      "Critical",
								Type:          "security",
								Provider:      "NVD",
								Description:   "Apache Log4j2 JNDI features do not protect against attacker controlled LDAP and other JNDI related endpoints.",
								CvssV3:        "10.0",
								FixedVersions: []string{"2.17.0"},
							},
						},
					},
				},
			},
		},
		Licenses: []License{
			{
				Name:       "Apache-2.0",
				Components: []string{"org.springframework:spring-boot-starter-web:2.7.0"},
				Violation:  false,
				Risk:       "Low",
			},
		},
	}

	return scanResult, nil
}

// BuildCollectEnv collects environment variables for build info
func (c *Client) BuildCollectEnv(ctx context.Context, buildName, buildNumber string) error {
	logrus.WithFields(logrus.Fields{
		"build_name":   buildName,
		"build_number": buildNumber,
	}).Debug("Collecting build environment")

	args := []string{"rt", "build-collect-env", buildName, buildNumber}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Build collect env output")

	if err != nil {
		return fmt.Errorf("build collect env failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// BuildAddGit adds git information to build info
func (c *Client) BuildAddGit(ctx context.Context, buildName, buildNumber, dotGitPath string) error {
	logrus.WithFields(logrus.Fields{
		"build_name":   buildName,
		"build_number": buildNumber,
		"git_path":     dotGitPath,
	}).Debug("Adding git information to build")

	args := []string{"rt", "build-add-git", buildName, buildNumber}
	if dotGitPath != "" {
		args = append(args, dotGitPath)
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Build add git output")

	if err != nil {
		return fmt.Errorf("build add git failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// BuildPublish publishes build info to Artifactory
func (c *Client) BuildPublish(ctx context.Context, buildName, buildNumber string) error {
	logrus.WithFields(logrus.Fields{
		"build_name":   buildName,
		"build_number": buildNumber,
	}).Info("Publishing build info to Artifactory")

	args := []string{"rt", "build-publish", buildName, buildNumber}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Build publish output")

	if err != nil {
		return fmt.Errorf("build publish failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// GetBuildInfo gets build information from Artifactory
func (c *Client) GetBuildInfo(ctx context.Context, buildName, buildNumber string) (*BuildInfo, error) {
	logrus.WithFields(logrus.Fields{
		"build_name":   buildName,
		"build_number": buildNumber,
	}).Debug("Getting build information")

	// For this example, we'll return simulated build info
	buildInfo := &BuildInfo{
		Version: "1.0.1",
		Name:    buildName,
		Number:  buildNumber,
		Started: "2025-07-17T14:30:15.123Z",
		BuildAgent: BuildAgent{
			Name:    "Generic",
			Version: "1.0.0",
		},
		VCS: []VCSInfo{
			{
				URL:      "https://github.com/company/project.git",
				Revision: "abc123def456",
				Branch:   "main",
				Message:  "Latest commit message",
			},
		},
		Modules: []Module{
			{
				ID: "org.example:myapp:1.0.0",
				Artifacts: []ModuleArtifact{
					{
						Type:   "jar",
						SHA1:   "da39a3ee5e6b4b0d3255bfef95601890afd80709",
						SHA256: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
						MD5:    "d41d8cd98f00b204e9800998ecf8427e",
						Name:   "myapp-1.0.0.jar",
						Path:   "org/example/myapp/1.0.0/myapp-1.0.0.jar",
					},
				},
			},
		},
	}

	return buildInfo, nil
}

// CreateReleaseBundle creates a release bundle for distribution
func (c *Client) CreateReleaseBundle(ctx context.Context, bundleName, version string, spec map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"bundle_name": bundleName,
		"version":     version,
	}).Info("Creating release bundle")

	args := []string{"ds", "release-bundle-create", bundleName, version}

	if specFile, ok := spec["spec_file"]; ok {
		args = append(args, "--spec", fmt.Sprintf("%v", specFile))
	}

	if sign, ok := spec["sign"]; ok && sign.(bool) {
		args = append(args, "--sign")
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Create release bundle output")

	if err != nil {
		return fmt.Errorf("create release bundle failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// DistributeReleaseBundle distributes a release bundle to target sites
func (c *Client) DistributeReleaseBundle(ctx context.Context, bundleName, version string, targets []string) error {
	logrus.WithFields(logrus.Fields{
		"bundle_name": bundleName,
		"version":     version,
		"targets":     targets,
	}).Info("Distributing release bundle")

	args := []string{"ds", "release-bundle-distribute", bundleName, version}

	for _, target := range targets {
		args = append(args, "--site", target)
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Distribute release bundle output")

	if err != nil {
		return fmt.Errorf("distribute release bundle failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// ListRepositories lists repositories in Artifactory
func (c *Client) ListRepositories(ctx context.Context) ([]Repository, error) {
	logrus.Debug("Listing repositories")

	// For this example, we'll return simulated repository list
	repositories := []Repository{
		{
			Key:         "docker-local",
			Type:        "LOCAL",
			PackageType: "Docker",
			Description: "Local Docker repository",
		},
		{
			Key:         "maven-central",
			Type:        "REMOTE",
			PackageType: "Maven",
			Description: "Maven Central remote repository",
		},
		{
			Key:         "libs-release",
			Type:        "VIRTUAL",
			PackageType: "Maven",
			Description: "Virtual Maven repository",
		},
	}

	return repositories, nil
}

// Health checks the health of JFrog services
func (c *Client) Health(ctx context.Context) error {
	logrus.Debug("Checking JFrog health")

	// Check Artifactory ping
	if err := c.ping(); err != nil {
		return fmt.Errorf("Artifactory health check failed: %w", err)
	}

	// Check Xray availability (if configured)
	cmd := exec.CommandContext(ctx, "jf", "xr", "ping")
	if err := cmd.Run(); err != nil {
		logrus.WithError(err).Warn("Xray not available or not configured")
	} else {
		logrus.Debug("Xray health check passed")
	}

	return nil
}

// ReleaseBundleV2 represents a Release Bundle V2
type ReleaseBundleV2 struct {
	Name         string              `json:"name"`
	Version      string              `json:"version"`
	Description  string              `json:"description"`
	ReleaseNotes ReleaseNotes        `json:"release_notes"`
	Source       ReleaseBundleSource `json:"source"`
	Spec         ReleaseBundleSpec   `json:"spec"`
	State        string              `json:"state"`
	Created      string              `json:"created"`
	CreatedBy    string              `json:"created_by"`
	Modified     string              `json:"modified"`
	ModifiedBy   string              `json:"modified_by"`
}

// ReleaseNotes represents release notes for a bundle
type ReleaseNotes struct {
	Syntax  string `json:"syntax"`
	Content string `json:"content"`
}

// ReleaseBundleSource represents the source of a release bundle
type ReleaseBundleSource struct {
	Type      string                  `json:"type"`
	Builds    []ReleaseBundleBuild    `json:"builds,omitempty"`
	Artifacts []ReleaseBundleArtifact `json:"artifacts,omitempty"`
}

// ReleaseBundleBuild represents a build in a release bundle
type ReleaseBundleBuild struct {
	Name    string `json:"name"`
	Number  string `json:"number"`
	Project string `json:"project,omitempty"`
}

// ReleaseBundleArtifact represents an artifact in a release bundle
type ReleaseBundleArtifact struct {
	Path       string            `json:"path"`
	Repository string            `json:"repository"`
	Properties map[string]string `json:"properties,omitempty"`
}

// ReleaseBundleSpec represents the specification for a release bundle
type ReleaseBundleSpec struct {
	Queries []ReleaseBundleQuery `json:"queries"`
}

// ReleaseBundleQuery represents a query for selecting artifacts
type ReleaseBundleQuery struct {
	AQL        string            `json:"aql,omitempty"`
	QueryName  string            `json:"query_name,omitempty"`
	Mappings   []ArtifactMapping `json:"mappings,omitempty"`
	AddedProps []Property        `json:"added_props,omitempty"`
}

// ArtifactMapping represents artifact path mapping
type ArtifactMapping struct {
	Input  string `json:"input"`
	Output string `json:"output"`
}

// Property represents a property key-value pair
type Property struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// DistributionTarget represents a distribution target
type DistributionTarget struct {
	Name        string `json:"name"`
	ServiceID   string `json:"service_id"`
	SiteName    string `json:"site_name"`
	CityName    string `json:"city_name"`
	CountryCode string `json:"country_code"`
}

// TransferSettings represents file transfer settings
type TransferSettings struct {
	SourceRepo      string            `json:"source_repo"`
	TargetRepo      string            `json:"target_repo"`
	SourceServer    string            `json:"source_server,omitempty"`
	TargetServer    string            `json:"target_server,omitempty"`
	IncludePatterns []string          `json:"include_patterns,omitempty"`
	ExcludePatterns []string          `json:"exclude_patterns,omitempty"`
	Properties      map[string]string `json:"properties,omitempty"`
}

// CreateReleaseBundleV2 creates a Release Bundle V2
func (c *Client) CreateReleaseBundleV2(ctx context.Context, bundleName, version string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"bundle_name": bundleName,
		"version":     version,
	}).Info("Creating Release Bundle V2")

	args := []string{"rt", "release-bundle-create", bundleName, version}

	// Add optional parameters
	if specFile, ok := options["spec_file"]; ok {
		args = append(args, "--spec", fmt.Sprintf("%v", specFile))
	}

	if buildsFile, ok := options["builds_file"]; ok {
		args = append(args, "--builds", fmt.Sprintf("%v", buildsFile))
	}

	if project, ok := options["project"]; ok {
		args = append(args, "--project", fmt.Sprintf("%v", project))
	}

	if releaseNotes, ok := options["release_notes"]; ok {
		args = append(args, "--release-notes-syntax", "markdown")
		args = append(args, "--release-notes", fmt.Sprintf("%v", releaseNotes))
	}

	if description, ok := options["description"]; ok {
		args = append(args, "--desc", fmt.Sprintf("%v", description))
	}

	if sign, ok := options["sign"]; ok && sign.(bool) {
		args = append(args, "--sign")
	}

	if gpgPassphrase, ok := options["gpg_passphrase"]; ok {
		args = append(args, "--gpg-passphrase", fmt.Sprintf("%v", gpgPassphrase))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Create Release Bundle V2 output")

	if err != nil {
		return fmt.Errorf("create release bundle v2 failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// DistributeReleaseBundleV2 distributes a Release Bundle V2 to target sites
func (c *Client) DistributeReleaseBundleV2(ctx context.Context, bundleName, version string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"bundle_name": bundleName,
		"version":     version,
	}).Info("Distributing Release Bundle V2")

	args := []string{"rt", "release-bundle-distribute", bundleName, version}

	// Add distribution targets
	if targets, ok := options["targets"]; ok {
		if targetList, ok := targets.([]string); ok {
			for _, target := range targetList {
				args = append(args, "--site", target)
			}
		}
	}

	if distRules, ok := options["dist_rules"]; ok {
		args = append(args, "--dist-rules", fmt.Sprintf("%v", distRules))
	}

	if project, ok := options["project"]; ok {
		args = append(args, "--project", fmt.Sprintf("%v", project))
	}

	if sync, ok := options["sync"]; ok && sync.(bool) {
		args = append(args, "--sync")
	}

	if maxWaitMinutes, ok := options["max_wait_minutes"]; ok {
		args = append(args, "--max-wait-minutes", fmt.Sprintf("%v", maxWaitMinutes))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Distribute Release Bundle V2 output")

	if err != nil {
		return fmt.Errorf("distribute release bundle v2 failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// AbortReleaseBundleV2 aborts a Release Bundle V2 distribution
func (c *Client) AbortReleaseBundleV2(ctx context.Context, bundleName, version string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"bundle_name": bundleName,
		"version":     version,
	}).Info("Aborting Release Bundle V2 distribution")

	args := []string{"rt", "release-bundle-distribute", bundleName, version, "--abort"}

	if project, ok := options["project"]; ok {
		args = append(args, "--project", fmt.Sprintf("%v", project))
	}

	if site, ok := options["site"]; ok {
		args = append(args, "--site", fmt.Sprintf("%v", site))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Abort Release Bundle V2 output")

	if err != nil {
		return fmt.Errorf("abort release bundle v2 failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// AnnotateReleaseBundleV2 adds annotations to a Release Bundle V2
func (c *Client) AnnotateReleaseBundleV2(ctx context.Context, bundleName, version string, annotations map[string]string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"bundle_name": bundleName,
		"version":     version,
		"annotations": annotations,
	}).Info("Annotating Release Bundle V2")

	args := []string{"rt", "release-bundle-annotate", bundleName, version}

	// Add annotations
	for key, value := range annotations {
		args = append(args, "--annotation", fmt.Sprintf("%s=%s", key, value))
	}

	if project, ok := options["project"]; ok {
		args = append(args, "--project", fmt.Sprintf("%v", project))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Annotate Release Bundle V2 output")

	if err != nil {
		return fmt.Errorf("annotate release bundle v2 failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// TransferFiles transfers files between Artifactory servers or repositories
func (c *Client) TransferFiles(ctx context.Context, settings TransferSettings) error {
	logrus.WithFields(logrus.Fields{
		"source_repo":   settings.SourceRepo,
		"target_repo":   settings.TargetRepo,
		"source_server": settings.SourceServer,
		"target_server": settings.TargetServer,
	}).Info("Transferring files")

	args := []string{"rt", "transfer-files"}

	// Add source and target
	if settings.SourceServer != "" {
		args = append(args, "--source-server", settings.SourceServer)
	}

	if settings.TargetServer != "" {
		args = append(args, "--target-server", settings.TargetServer)
	}

	args = append(args, settings.SourceRepo, settings.TargetRepo)

	// Add include patterns
	for _, pattern := range settings.IncludePatterns {
		args = append(args, "--include", pattern)
	}

	// Add exclude patterns
	for _, pattern := range settings.ExcludePatterns {
		args = append(args, "--exclude", pattern)
	}

	// Add properties
	for key, value := range settings.Properties {
		args = append(args, "--props", fmt.Sprintf("%s=%s", key, value))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Transfer files output")

	if err != nil {
		return fmt.Errorf("transfer files failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// DownloadFiles downloads files with advanced options
func (c *Client) DownloadFiles(ctx context.Context, source, target string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"source": source,
		"target": target,
	}).Info("Downloading files with advanced options")

	args := []string{"rt", "download", source}

	if target != "" {
		args = append(args, target)
	}

	// Add optional parameters
	if threads, ok := options["threads"]; ok {
		args = append(args, "--threads", fmt.Sprintf("%v", threads))
	}

	if splitCount, ok := options["split_count"]; ok {
		args = append(args, "--split-count", fmt.Sprintf("%v", splitCount))
	}

	if minSplit, ok := options["min_split"]; ok {
		args = append(args, "--min-split", fmt.Sprintf("%v", minSplit))
	}

	if retries, ok := options["retries"]; ok {
		args = append(args, "--retries", fmt.Sprintf("%v", retries))
	}

	if retryWaitTime, ok := options["retry_wait_time"]; ok {
		args = append(args, "--retry-wait-time", fmt.Sprintf("%v", retryWaitTime))
	}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if module, ok := options["module"]; ok {
		args = append(args, "--module", fmt.Sprintf("%v", module))
	}

	if validate, ok := options["validate"]; ok && validate.(bool) {
		args = append(args, "--validate-checksums")
	}

	if syncDeletes, ok := options["sync_deletes"]; ok && syncDeletes.(bool) {
		args = append(args, "--sync-deletes")
	}

	if quiet, ok := options["quiet"]; ok && quiet.(bool) {
		args = append(args, "--quiet")
	}

	if excludePatterns, ok := options["exclude_patterns"]; ok {
		if patterns, ok := excludePatterns.([]string); ok {
			for _, pattern := range patterns {
				args = append(args, "--exclude-patterns", pattern)
			}
		}
	}

	if includePatterns, ok := options["include_patterns"]; ok {
		if patterns, ok := includePatterns.([]string); ok {
			for _, pattern := range patterns {
				args = append(args, "--include-patterns", pattern)
			}
		}
	}

	if archiveEntries, ok := options["archive_entries"]; ok {
		args = append(args, "--archive-entries", fmt.Sprintf("%v", archiveEntries))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Download files output")

	if err != nil {
		return fmt.Errorf("download files failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// CreateReleaseBundleFromBuilds creates a Release Bundle V2 from builds
func (c *Client) CreateReleaseBundleFromBuilds(ctx context.Context, bundleName, version string, builds []ReleaseBundleBuild, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"bundle_name": bundleName,
		"version":     version,
		"builds":      builds,
	}).Info("Creating Release Bundle V2 from builds")

	args := []string{"rt", "release-bundle-create", bundleName, version}

	// Create builds specification
	buildsSpec := map[string]interface{}{
		"builds": builds,
	}

	// Convert to JSON and write to temp file
	buildsJSON, err := json.Marshal(buildsSpec)
	if err != nil {
		return fmt.Errorf("failed to marshal builds spec: %w", err)
	}

	// For simplicity, we'll pass builds as a parameter
	// In a real implementation, you'd write to a temp file
	args = append(args, "--builds-spec", string(buildsJSON))

	if project, ok := options["project"]; ok {
		args = append(args, "--project", fmt.Sprintf("%v", project))
	}

	if releaseNotes, ok := options["release_notes"]; ok {
		args = append(args, "--release-notes-syntax", "markdown")
		args = append(args, "--release-notes", fmt.Sprintf("%v", releaseNotes))
	}

	if description, ok := options["description"]; ok {
		args = append(args, "--desc", fmt.Sprintf("%v", description))
	}

	if sign, ok := options["sign"]; ok && sign.(bool) {
		args = append(args, "--sign")
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Create Release Bundle from builds output")

	if err != nil {
		return fmt.Errorf("create release bundle from builds failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// GetReleaseBundleV2 gets information about a Release Bundle V2
func (c *Client) GetReleaseBundleV2(ctx context.Context, bundleName, version string, options map[string]interface{}) (*ReleaseBundleV2, error) {
	logrus.WithFields(logrus.Fields{
		"bundle_name": bundleName,
		"version":     version,
	}).Debug("Getting Release Bundle V2 information")

	// For this example, we'll return simulated release bundle info
	// In a real implementation, you'd call the JFrog CLI or REST API
	releaseBundle := &ReleaseBundleV2{
		Name:        bundleName,
		Version:     version,
		Description: "Example release bundle",
		State:       "SIGNED",
		Created:     "2025-07-17T14:30:15.123Z",
		CreatedBy:   "admin",
		Modified:    "2025-07-17T14:30:15.123Z",
		ModifiedBy:  "admin",
		ReleaseNotes: ReleaseNotes{
			Syntax:  "markdown",
			Content: "# Release Notes\n\nThis is an example release bundle.",
		},
		Source: ReleaseBundleSource{
			Type: "builds",
			Builds: []ReleaseBundleBuild{
				{
					Name:    "my-build",
					Number:  "1.0.0",
					Project: "my-project",
				},
			},
		},
	}

	return releaseBundle, nil
}

// SetProperties sets properties on artifacts
func (c *Client) SetProperties(ctx context.Context, target string, properties map[string]string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"target":     target,
		"properties": properties,
	}).Info("Setting properties on artifacts")

	args := []string{"rt", "set-props", target}

	// Add properties
	var propStrings []string
	for key, value := range properties {
		propStrings = append(propStrings, fmt.Sprintf("%s=%s", key, value))
	}
	args = append(args, strings.Join(propStrings, ";"))

	if recursive, ok := options["recursive"]; ok && recursive.(bool) {
		args = append(args, "--recursive")
	}

	if includePatterns, ok := options["include_patterns"]; ok {
		if patterns, ok := includePatterns.([]string); ok {
			for _, pattern := range patterns {
				args = append(args, "--include-patterns", pattern)
			}
		}
	}

	if excludePatterns, ok := options["exclude_patterns"]; ok {
		if patterns, ok := excludePatterns.([]string); ok {
			for _, pattern := range patterns {
				args = append(args, "--exclude-patterns", pattern)
			}
		}
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Set properties output")

	if err != nil {
		return fmt.Errorf("set properties failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// DeleteProperties deletes properties from artifacts
func (c *Client) DeleteProperties(ctx context.Context, target string, propertyKeys []string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"target":        target,
		"property_keys": propertyKeys,
	}).Info("Deleting properties from artifacts")

	args := []string{"rt", "delete-props", target, strings.Join(propertyKeys, ",")}

	if recursive, ok := options["recursive"]; ok && recursive.(bool) {
		args = append(args, "--recursive")
	}

	if includePatterns, ok := options["include_patterns"]; ok {
		if patterns, ok := includePatterns.([]string); ok {
			for _, pattern := range patterns {
				args = append(args, "--include-patterns", pattern)
			}
		}
	}

	if excludePatterns, ok := options["exclude_patterns"]; ok {
		if patterns, ok := excludePatterns.([]string); ok {
			for _, pattern := range patterns {
				args = append(args, "--exclude-patterns", pattern)
			}
		}
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Delete properties output")

	if err != nil {
		return fmt.Errorf("delete properties failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// Copy copies artifacts within Artifactory
func (c *Client) Copy(ctx context.Context, source, target string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"source": source,
		"target": target,
	}).Info("Copying artifacts")

	args := []string{"rt", "copy", source, target}

	if recursive, ok := options["recursive"]; ok && recursive.(bool) {
		args = append(args, "--recursive")
	}

	if flat, ok := options["flat"]; ok && flat.(bool) {
		args = append(args, "--flat")
	}

	if threads, ok := options["threads"]; ok {
		args = append(args, "--threads", fmt.Sprintf("%v", threads))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Copy artifacts output")

	if err != nil {
		return fmt.Errorf("copy artifacts failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// Move moves artifacts within Artifactory
func (c *Client) Move(ctx context.Context, source, target string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"source": source,
		"target": target,
	}).Info("Moving artifacts")

	args := []string{"rt", "move", source, target}

	if recursive, ok := options["recursive"]; ok && recursive.(bool) {
		args = append(args, "--recursive")
	}

	if flat, ok := options["flat"]; ok && flat.(bool) {
		args = append(args, "--flat")
	}

	if threads, ok := options["threads"]; ok {
		args = append(args, "--threads", fmt.Sprintf("%v", threads))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Move artifacts output")

	if err != nil {
		return fmt.Errorf("move artifacts failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// Delete deletes artifacts from Artifactory
func (c *Client) Delete(ctx context.Context, target string, options map[string]interface{}) error {
	logrus.WithField("target", target).Info("Deleting artifacts")

	args := []string{"rt", "delete", target}

	if recursive, ok := options["recursive"]; ok && recursive.(bool) {
		args = append(args, "--recursive")
	}

	if quiet, ok := options["quiet"]; ok && quiet.(bool) {
		args = append(args, "--quiet")
	}

	if threads, ok := options["threads"]; ok {
		args = append(args, "--threads", fmt.Sprintf("%v", threads))
	}

	if dryRun, ok := options["dry_run"]; ok && dryRun.(bool) {
		args = append(args, "--dry-run")
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Delete artifacts output")

	if err != nil {
		return fmt.Errorf("delete artifacts failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// BuildPromote promotes a build in Artifactory
func (c *Client) BuildPromote(ctx context.Context, buildName, buildNumber, targetRepo string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"build_name":   buildName,
		"build_number": buildNumber,
		"target_repo":  targetRepo,
	}).Info("Promoting build")

	args := []string{"rt", "build-promote", buildName, buildNumber, targetRepo}

	if status, ok := options["status"]; ok {
		args = append(args, "--status", fmt.Sprintf("%v", status))
	}

	if comment, ok := options["comment"]; ok {
		args = append(args, "--comment", fmt.Sprintf("%v", comment))
	}

	if sourceRepo, ok := options["source_repo"]; ok {
		args = append(args, "--source-repo", fmt.Sprintf("%v", sourceRepo))
	}

	if includeDependencies, ok := options["include_dependencies"]; ok && includeDependencies.(bool) {
		args = append(args, "--include-dependencies")
	}

	if copy, ok := options["copy"]; ok && copy.(bool) {
		args = append(args, "--copy")
	}

	if dryRun, ok := options["dry_run"]; ok && dryRun.(bool) {
		args = append(args, "--dry-run")
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Build promote output")

	if err != nil {
		return fmt.Errorf("build promote failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// BuildDiscard discards old builds from Artifactory
func (c *Client) BuildDiscard(ctx context.Context, buildName string, options map[string]interface{}) error {
	logrus.WithField("build_name", buildName).Info("Discarding old builds")

	args := []string{"rt", "build-discard", buildName}

	if maxBuilds, ok := options["max_builds"]; ok {
		args = append(args, "--max-builds", fmt.Sprintf("%v", maxBuilds))
	}

	if maxDays, ok := options["max_days"]; ok {
		args = append(args, "--max-days", fmt.Sprintf("%v", maxDays))
	}

	if excludeBuilds, ok := options["exclude_builds"]; ok {
		args = append(args, "--exclude-builds", fmt.Sprintf("%v", excludeBuilds))
	}

	if deleteArtifacts, ok := options["delete_artifacts"]; ok && deleteArtifacts.(bool) {
		args = append(args, "--delete-artifacts")
	}

	if async, ok := options["async"]; ok && async.(bool) {
		args = append(args, "--async")
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Build discard output")

	if err != nil {
		return fmt.Errorf("build discard failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// BuildScan scans a build with Xray
func (c *Client) BuildScan(ctx context.Context, buildName, buildNumber string, options map[string]interface{}) (*ScanResult, error) {
	logrus.WithFields(logrus.Fields{
		"build_name":   buildName,
		"build_number": buildNumber,
	}).Info("Scanning build with Xray")

	args := []string{"xr", "build-scan", buildName, buildNumber}

	if vuln, ok := options["vuln"]; ok && vuln.(bool) {
		args = append(args, "--vuln")
	}

	if license, ok := options["license"]; ok && license.(bool) {
		args = append(args, "--license")
	}

	if fail, ok := options["fail"]; ok && fail.(bool) {
		args = append(args, "--fail")
	}

	if format, ok := options["format"]; ok {
		args = append(args, "--format", fmt.Sprintf("%v", format))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("build scan failed: %w\nOutput: %s", err, string(output))
	}

	// For this example, we'll return simulated scan results
	// In a real implementation, you'd parse the actual output
	scanResult := &ScanResult{
		Summary: ScanSummary{
			TotalAlerts: 2,
			FailBuild:   false,
			Message:     "Build scan completed successfully",
		},
		Alerts: []Alert{
			{
				TopSeverity: "Medium",
				Components: []Component{
					{
						ComponentID: "npm://lodash:4.17.20",
						Issues: []Issue{
							{
								IssueID:       "CVE-2021-23337",
								Severity:      "Medium",
								Type:          "security",
								Provider:      "NVD",
								Description:   "Lodash versions prior to 4.17.21 are vulnerable to Command Injection via template.",
								CvssV3:        "7.2",
								FixedVersions: []string{"4.17.21"},
							},
						},
					},
				},
			},
		},
		Licenses: []License{
			{
				Name:       "MIT",
				Components: []string{"npm://lodash:4.17.20"},
				Violation:  false,
				Risk:       "Low",
			},
		},
	}

	return scanResult, nil
}

// GetSystemInfo gets system information from Artifactory
func (c *Client) GetSystemInfo(ctx context.Context) (*ArtifactoryInfo, error) {
	logrus.Debug("Getting Artifactory system information")

	cmd := exec.CommandContext(ctx, "jf", "rt", "ping", "--url", c.config.Environments.Online.JFrog.URL)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to get system info: %w", err)
	}

	// For this example, we'll return simulated system info
	// In a real implementation, you'd parse the actual response
	systemInfo := &ArtifactoryInfo{
		Version:     "7.77.5",
		Revision:    "77705900",
		License:     "Artifactory Pro",
		BuildNumber: "77705900",
	}

	logrus.WithField("response", strings.TrimSpace(string(output))).Debug("System info retrieved")
	return systemInfo, nil
}

// CreateAccessToken creates an access token
func (c *Client) CreateAccessToken(ctx context.Context, options map[string]interface{}) (string, error) {
	logrus.Info("Creating access token")

	args := []string{"rt", "access-token-create"}

	if username, ok := options["username"]; ok {
		args = append(args, "--user", fmt.Sprintf("%v", username))
	}

	if groups, ok := options["groups"]; ok {
		if groupList, ok := groups.([]string); ok {
			args = append(args, "--groups", strings.Join(groupList, ","))
		}
	}

	if expiry, ok := options["expiry"]; ok {
		args = append(args, "--expiry", fmt.Sprintf("%v", expiry))
	}

	if refreshable, ok := options["refreshable"]; ok && refreshable.(bool) {
		args = append(args, "--refreshable")
	}

	if audience, ok := options["audience"]; ok {
		args = append(args, "--audience", fmt.Sprintf("%v", audience))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("create access token failed: %w\nOutput: %s", err, string(output))
	}

	token := strings.TrimSpace(string(output))
	logrus.Debug("Access token created successfully")
	return token, nil
}
