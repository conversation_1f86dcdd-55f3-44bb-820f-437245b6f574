// Package: jfrog
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:jfrog-package-ops:2025-01-27T10:00:00.123Z
//
// Purpose: JFrog package management operations - Docker, NPM, Go, Python, Maven, Gradle
// Governance: MCStack v13.5 compliance with enterprise security policies
// Security: Secure package management with proper validation and error handling
package jfrog

import (
	"context"
	"fmt"
	"os/exec"

	"github.com/sirupsen/logrus"
)

// DockerPush pushes Docker images to Artifactory
func (c *Client) DockerPush(ctx context.Context, image, targetRepo string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"image":       image,
		"target_repo": targetRepo,
	}).Info("Pushing Docker image to Artifactory")

	args := []string{"rt", "docker-push", image, targetRepo}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if module, ok := options["module"]; ok {
		args = append(args, "--module", fmt.Sprintf("%v", module))
	}

	if threads, ok := options["threads"]; ok {
		args = append(args, "--threads", fmt.Sprintf("%v", threads))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Docker push output")

	if err != nil {
		return fmt.Errorf("docker push failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// DockerPull pulls Docker images from Artifactory
func (c *Client) DockerPull(ctx context.Context, image, sourceRepo string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"image":       image,
		"source_repo": sourceRepo,
	}).Info("Pulling Docker image from Artifactory")

	args := []string{"rt", "docker-pull", fmt.Sprintf("%s/%s", sourceRepo, image)}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if module, ok := options["module"]; ok {
		args = append(args, "--module", fmt.Sprintf("%v", module))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Docker pull output")

	if err != nil {
		return fmt.Errorf("docker pull failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// NpmPublish publishes npm packages to Artifactory
func (c *Client) NpmPublish(ctx context.Context, packagePath, targetRepo string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"package_path": packagePath,
		"target_repo":  targetRepo,
	}).Info("Publishing npm package to Artifactory")

	args := []string{"rt", "npm-publish", packagePath, targetRepo}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if module, ok := options["module"]; ok {
		args = append(args, "--module", fmt.Sprintf("%v", module))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("NPM publish output")

	if err != nil {
		return fmt.Errorf("npm publish failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// NpmInstall installs npm packages from Artifactory
func (c *Client) NpmInstall(ctx context.Context, sourceRepo string, options map[string]interface{}) error {
	logrus.WithField("source_repo", sourceRepo).Info("Installing npm packages from Artifactory")

	args := []string{"rt", "npm-install", sourceRepo}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if module, ok := options["module"]; ok {
		args = append(args, "--module", fmt.Sprintf("%v", module))
	}

	if threads, ok := options["threads"]; ok {
		args = append(args, "--threads", fmt.Sprintf("%v", threads))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("NPM install output")

	if err != nil {
		return fmt.Errorf("npm install failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// GoPublish publishes Go modules to Artifactory
func (c *Client) GoPublish(ctx context.Context, targetRepo, version string, options map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"target_repo": targetRepo,
		"version":     version,
	}).Info("Publishing Go module to Artifactory")

	args := []string{"rt", "go-publish", targetRepo, version}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if module, ok := options["module"]; ok {
		args = append(args, "--module", fmt.Sprintf("%v", module))
	}

	if deps, ok := options["deps"]; ok {
		args = append(args, "--deps", fmt.Sprintf("%v", deps))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Go publish output")

	if err != nil {
		return fmt.Errorf("go publish failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// PipInstall installs Python packages from Artifactory
func (c *Client) PipInstall(ctx context.Context, sourceRepo string, options map[string]interface{}) error {
	logrus.WithField("source_repo", sourceRepo).Info("Installing Python packages from Artifactory")

	args := []string{"rt", "pip-install", sourceRepo}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if module, ok := options["module"]; ok {
		args = append(args, "--module", fmt.Sprintf("%v", module))
	}

	if requirements, ok := options["requirements"]; ok {
		args = append(args, "--requirements", fmt.Sprintf("%v", requirements))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Pip install output")

	if err != nil {
		return fmt.Errorf("pip install failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// MavenDeploy deploys Maven artifacts to Artifactory
func (c *Client) MavenDeploy(ctx context.Context, targetRepo string, options map[string]interface{}) error {
	logrus.WithField("target_repo", targetRepo).Info("Deploying Maven artifacts to Artifactory")

	args := []string{"rt", "mvn-deploy", targetRepo}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if module, ok := options["module"]; ok {
		args = append(args, "--module", fmt.Sprintf("%v", module))
	}

	if threads, ok := options["threads"]; ok {
		args = append(args, "--threads", fmt.Sprintf("%v", threads))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Maven deploy output")

	if err != nil {
		return fmt.Errorf("maven deploy failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}

// GradleDeploy deploys Gradle artifacts to Artifactory
func (c *Client) GradleDeploy(ctx context.Context, targetRepo string, options map[string]interface{}) error {
	logrus.WithField("target_repo", targetRepo).Info("Deploying Gradle artifacts to Artifactory")

	args := []string{"rt", "gradle-deploy", targetRepo}

	if buildName, ok := options["build_name"]; ok {
		args = append(args, "--build-name", fmt.Sprintf("%v", buildName))
	}

	if buildNumber, ok := options["build_number"]; ok {
		args = append(args, "--build-number", fmt.Sprintf("%v", buildNumber))
	}

	if module, ok := options["module"]; ok {
		args = append(args, "--module", fmt.Sprintf("%v", module))
	}

	if threads, ok := options["threads"]; ok {
		args = append(args, "--threads", fmt.Sprintf("%v", threads))
	}

	cmd := exec.CommandContext(ctx, "jf", args...)
	output, err := cmd.CombinedOutput()

	logrus.WithField("output", string(output)).Debug("Gradle deploy output")

	if err != nil {
		return fmt.Errorf("gradle deploy failed: %w\nOutput: %s", err, string(output))
	}

	return nil
}
