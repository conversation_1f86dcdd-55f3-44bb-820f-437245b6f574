// Package: jfrog
// Agent: AG-013 (<PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:jfrog-test-runner:2025-01-27T10:00:00.123Z
//
// Purpose: Test runner and validation utilities for JFrog client testing
// Governance: MCStack v13.5 compliance with comprehensive test orchestration
// Security: Secure test execution with proper isolation and cleanup
//go:build test_runner
// +build test_runner

package jfrog

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/sirupsen/logrus"
)

// TestRunner orchestrates comprehensive testing of the JFrog client
type TestRunner struct {
	config      *config.Config
	tempDir     string
	ctx         context.Context
	results     *TestResults
	jfrogCLI    bool
	jfrogServer bool
}

// TestResults tracks test execution results
type TestResults struct {
	UnitTests        TestSuiteResult
	IntegrationTests TestSuiteResult
	E2ETests         TestSuiteResult
	TotalDuration    time.Duration
	StartTime        time.Time
	EndTime          time.Time
}

// TestSuiteResult represents results for a test suite
type TestSuiteResult struct {
	Name     string
	Passed   int
	Failed   int
	Skipped  int
	Duration time.Duration
	Errors   []string
	Executed bool
}

// NewTestRunner creates a new test runner instance
func NewTestRunner() (*TestRunner, error) {
	tempDir, err := os.MkdirTemp("", "jfrog-test-runner-*")
	if err != nil {
		return nil, fmt.Errorf("failed to create temp directory: %w", err)
	}

	config := &config.Config{
		Environments: config.EnvironmentsConfig{
			Online: config.OnlineEnvironment{
				JFrog: config.JFrogConfig{
					URL:      getEnvOrDefault("JFROG_URL", "https://test.jfrog.io"),
					Username: getEnvOrDefault("JFROG_USERNAME", "test-user"),
					Password: getEnvOrDefault("JFROG_PASSWORD", "test-password"),
				},
			},
		},
	}

	runner := &TestRunner{
		config:  config,
		tempDir: tempDir,
		ctx:     context.Background(),
		results: &TestResults{
			StartTime: time.Now(),
		},
	}

	// Check prerequisites
	runner.checkPrerequisites()

	return runner, nil
}

// checkPrerequisites verifies test environment setup
func (tr *TestRunner) checkPrerequisites() {
	logrus.Info("Checking test prerequisites...")

	// Check JFrog CLI availability
	if _, err := exec.LookPath("jf"); err != nil {
		logrus.Warn("JFrog CLI not found in PATH")
		tr.jfrogCLI = false
	} else {
		logrus.Info("JFrog CLI is available")
		tr.jfrogCLI = true
	}

	// Check JFrog server connectivity (if CLI is available)
	if tr.jfrogCLI {
		ctx, cancel := context.WithTimeout(tr.ctx, 10*time.Second)
		defer cancel()

		cmd := exec.CommandContext(ctx, "jf", "rt", "ping", "--url", tr.config.Environments.Online.JFrog.URL)
		if err := cmd.Run(); err != nil {
			logrus.Warn("JFrog server not accessible")
			tr.jfrogServer = false
		} else {
			logrus.Info("JFrog server is accessible")
			tr.jfrogServer = true
		}
	}
}

// RunAllTests executes all test suites
func (tr *TestRunner) RunAllTests() error {
	logrus.Info("Starting comprehensive JFrog client testing...")

	// Run unit tests
	if err := tr.runUnitTests(); err != nil {
		logrus.WithError(err).Error("Unit tests failed")
	}

	// Run integration tests (if JFrog CLI is available)
	if tr.jfrogCLI {
		if err := tr.runIntegrationTests(); err != nil {
			logrus.WithError(err).Error("Integration tests failed")
		}
	} else {
		logrus.Info("Skipping integration tests - JFrog CLI not available")
		tr.results.IntegrationTests.Skipped = 1
	}

	// Run E2E tests (if both CLI and server are available)
	if tr.jfrogCLI && tr.jfrogServer {
		if err := tr.runE2ETests(); err != nil {
			logrus.WithError(err).Error("E2E tests failed")
		}
	} else {
		logrus.Info("Skipping E2E tests - JFrog services not fully available")
		tr.results.E2ETests.Skipped = 1
	}

	tr.results.EndTime = time.Now()
	tr.results.TotalDuration = tr.results.EndTime.Sub(tr.results.StartTime)

	// Generate test report
	tr.generateReport()

	return nil
}

// runUnitTests executes unit tests
func (tr *TestRunner) runUnitTests() error {
	logrus.Info("Running unit tests...")
	start := time.Now()

	// Run unit tests using go test
	cmd := exec.Command("go", "test", "-v", "-tags=unit", "./internal/jfrog/...")
	cmd.Dir = tr.getProjectRoot()

	output, err := cmd.CombinedOutput()

	tr.results.UnitTests = TestSuiteResult{
		Name:     "Unit Tests",
		Duration: time.Since(start),
		Executed: true,
	}

	if err != nil {
		tr.results.UnitTests.Failed = 1
		tr.results.UnitTests.Errors = append(tr.results.UnitTests.Errors, string(output))
		return fmt.Errorf("unit tests failed: %w", err)
	}

	tr.results.UnitTests.Passed = 1
	logrus.Info("Unit tests completed successfully")
	return nil
}

// runIntegrationTests executes integration tests
func (tr *TestRunner) runIntegrationTests() error {
	logrus.Info("Running integration tests...")
	start := time.Now()

	// Set environment variables for integration tests
	env := os.Environ()
	env = append(env, fmt.Sprintf("JFROG_URL=%s", tr.config.Environments.Online.JFrog.URL))
	env = append(env, fmt.Sprintf("JFROG_USERNAME=%s", tr.config.Environments.Online.JFrog.Username))
	env = append(env, fmt.Sprintf("JFROG_PASSWORD=%s", tr.config.Environments.Online.JFrog.Password))

	cmd := exec.Command("go", "test", "-v", "-tags=integration", "./internal/jfrog/...")
	cmd.Dir = tr.getProjectRoot()
	cmd.Env = env

	output, err := cmd.CombinedOutput()

	tr.results.IntegrationTests = TestSuiteResult{
		Name:     "Integration Tests",
		Duration: time.Since(start),
		Executed: true,
	}

	if err != nil {
		tr.results.IntegrationTests.Failed = 1
		tr.results.IntegrationTests.Errors = append(tr.results.IntegrationTests.Errors, string(output))
		return fmt.Errorf("integration tests failed: %w", err)
	}

	tr.results.IntegrationTests.Passed = 1
	logrus.Info("Integration tests completed successfully")
	return nil
}

// runE2ETests executes end-to-end tests
func (tr *TestRunner) runE2ETests() error {
	logrus.Info("Running E2E tests...")
	start := time.Now()

	// Set environment variables for E2E tests
	env := os.Environ()
	env = append(env, "RUN_E2E_TESTS=1")
	env = append(env, fmt.Sprintf("JFROG_URL=%s", tr.config.Environments.Online.JFrog.URL))
	env = append(env, fmt.Sprintf("JFROG_USERNAME=%s", tr.config.Environments.Online.JFrog.Username))
	env = append(env, fmt.Sprintf("JFROG_PASSWORD=%s", tr.config.Environments.Online.JFrog.Password))

	cmd := exec.Command("go", "test", "-v", "-tags=e2e", "-timeout=30m", "./internal/jfrog/...")
	cmd.Dir = tr.getProjectRoot()
	cmd.Env = env

	output, err := cmd.CombinedOutput()

	tr.results.E2ETests = TestSuiteResult{
		Name:     "E2E Tests",
		Duration: time.Since(start),
		Executed: true,
	}

	if err != nil {
		tr.results.E2ETests.Failed = 1
		tr.results.E2ETests.Errors = append(tr.results.E2ETests.Errors, string(output))
		return fmt.Errorf("E2E tests failed: %w", err)
	}

	tr.results.E2ETests.Passed = 1
	logrus.Info("E2E tests completed successfully")
	return nil
}

// generateReport creates a comprehensive test report
func (tr *TestRunner) generateReport() {
	logrus.Info("Generating test report...")

	report := strings.Builder{}
	report.WriteString("# JFrog Client Test Report\n\n")
	report.WriteString(fmt.Sprintf("**Generated:** %s\n", time.Now().Format(time.RFC3339)))
	report.WriteString(fmt.Sprintf("**Total Duration:** %s\n\n", tr.results.TotalDuration))

	// Prerequisites section
	report.WriteString("## Prerequisites\n\n")
	report.WriteString(fmt.Sprintf("- JFrog CLI Available: %t\n", tr.jfrogCLI))
	report.WriteString(fmt.Sprintf("- JFrog Server Accessible: %t\n", tr.jfrogServer))
	report.WriteString(fmt.Sprintf("- Test Environment: %s\n\n", tr.config.Environments.Online.JFrog.URL))

	// Test results section
	report.WriteString("## Test Results\n\n")

	suites := []TestSuiteResult{
		tr.results.UnitTests,
		tr.results.IntegrationTests,
		tr.results.E2ETests,
	}

	for _, suite := range suites {
		if !suite.Executed {
			continue
		}

		status := "✅ PASSED"
		if suite.Failed > 0 {
			status = "❌ FAILED"
		}
		if suite.Skipped > 0 {
			status = "⏭️ SKIPPED"
		}

		report.WriteString(fmt.Sprintf("### %s %s\n\n", suite.Name, status))
		report.WriteString(fmt.Sprintf("- **Duration:** %s\n", suite.Duration))
		report.WriteString(fmt.Sprintf("- **Passed:** %d\n", suite.Passed))
		report.WriteString(fmt.Sprintf("- **Failed:** %d\n", suite.Failed))
		report.WriteString(fmt.Sprintf("- **Skipped:** %d\n\n", suite.Skipped))

		if len(suite.Errors) > 0 {
			report.WriteString("**Errors:**\n```\n")
			for _, err := range suite.Errors {
				report.WriteString(err)
				report.WriteString("\n")
			}
			report.WriteString("```\n\n")
		}
	}

	// Summary section
	totalPassed := tr.results.UnitTests.Passed + tr.results.IntegrationTests.Passed + tr.results.E2ETests.Passed
	totalFailed := tr.results.UnitTests.Failed + tr.results.IntegrationTests.Failed + tr.results.E2ETests.Failed
	totalSkipped := tr.results.UnitTests.Skipped + tr.results.IntegrationTests.Skipped + tr.results.E2ETests.Skipped

	report.WriteString("## Summary\n\n")
	report.WriteString(fmt.Sprintf("- **Total Passed:** %d\n", totalPassed))
	report.WriteString(fmt.Sprintf("- **Total Failed:** %d\n", totalFailed))
	report.WriteString(fmt.Sprintf("- **Total Skipped:** %d\n", totalSkipped))
	report.WriteString(fmt.Sprintf("- **Overall Status:** %s\n\n", func() string {
		if totalFailed > 0 {
			return "❌ FAILED"
		}
		if totalPassed > 0 {
			return "✅ PASSED"
		}
		return "⏭️ SKIPPED"
	}()))

	// Write report to file
	reportPath := filepath.Join(tr.tempDir, "test-report.md")
	if err := os.WriteFile(reportPath, []byte(report.String()), 0644); err != nil {
		logrus.WithError(err).Error("Failed to write test report")
	} else {
		logrus.Infof("Test report written to: %s", reportPath)
	}

	// Print summary to console
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("JFrog Client Test Summary")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("Total Duration: %s\n", tr.results.TotalDuration)
	fmt.Printf("Passed: %d | Failed: %d | Skipped: %d\n", totalPassed, totalFailed, totalSkipped)
	fmt.Printf("Report: %s\n", reportPath)
	fmt.Println(strings.Repeat("=", 60))
}

// getProjectRoot returns the project root directory
func (tr *TestRunner) getProjectRoot() string {
	// This should be adjusted based on your project structure
	wd, _ := os.Getwd()
	for {
		if _, err := os.Stat(filepath.Join(wd, "go.mod")); err == nil {
			return wd
		}
		parent := filepath.Dir(wd)
		if parent == wd {
			break
		}
		wd = parent
	}
	return "."
}

// Cleanup removes temporary files and resources
func (tr *TestRunner) Cleanup() error {
	if tr.tempDir != "" {
		return os.RemoveAll(tr.tempDir)
	}
	return nil
}

// RunTests is the main entry point for running all tests
func RunTests() error {
	runner, err := NewTestRunner()
	if err != nil {
		return fmt.Errorf("failed to create test runner: %w", err)
	}
	defer runner.Cleanup()

	return runner.RunAllTests()
}
