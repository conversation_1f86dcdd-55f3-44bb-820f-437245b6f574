// Package: jfrog
// Agent: AG-013 (Claude <PERSON>)
// GAL: 2
// Coordinating Agents: [AG-001, AG-006, AG-005]
// URN: urn:mc:exec:tenant:claude-code:jfrog-integration-test:2025-01-27T10:00:00.123Z
//
// Purpose: Integration tests for JFrog client with real CLI commands
// Governance: MCStack v13.5 compliance with external service integration
// Security: Test real JFrog CLI integration with proper error handling
//go:build integration
// +build integration

package jfrog

import (
	"context"
	"os"
	"os/exec"
	"path/filepath"
	"testing"
	"time"

	"github.com/mchorfa/mc-poly-installer/pkg/config"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// IntegrationTestSuite runs integration tests with real JFrog CLI
type IntegrationTestSuite struct {
	suite.Suite
	client         *Client
	config         *config.Config
	ctx            context.Context
	tempDir        string
	jfrogAvailable bool
}

func (suite *IntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Check if JFrog CLI is available
	if _, err := exec.LookPath("jf"); err != nil {
		suite.T().Skip("JFrog CLI not available, skipping integration tests")
		return
	}
	suite.jfrogAvailable = true

	// Create temporary directory for test files
	tempDir, err := os.MkdirTemp("", "jfrog-integration-test-*")
	suite.Require().NoError(err)
	suite.tempDir = tempDir

	// Setup test configuration
	suite.config = &config.Config{
		Environments: config.EnvironmentsConfig{
			Online: config.OnlineEnvironment{
				JFrog: config.JFrogConfig{
					URL:      getEnvOrDefault("JFROG_URL", "https://test.jfrog.io"),
					Username: getEnvOrDefault("JFROG_USERNAME", "test-user"),
					Password: getEnvOrDefault("JFROG_PASSWORD", "test-password"),
				},
			},
		},
	}

	// Set log level for integration tests
	logrus.SetLevel(logrus.InfoLevel)
}

func (suite *IntegrationTestSuite) TearDownSuite() {
	if suite.tempDir != "" {
		os.RemoveAll(suite.tempDir)
	}
}

func (suite *IntegrationTestSuite) SetupTest() {
	if !suite.jfrogAvailable {
		suite.T().Skip("JFrog CLI not available")
		return
	}

	// Create client for each test
	var err error
	suite.client, err = NewClient(suite.config)

	// Client creation may fail if JFrog server is not accessible
	// This is expected in CI environments
	if err != nil {
		suite.T().Logf("JFrog client creation failed (expected in CI): %v", err)
		suite.T().Skip("JFrog server not accessible")
		return
	}
}

func (suite *IntegrationTestSuite) TestJFrogCLIVersion() {
	if !suite.jfrogAvailable {
		suite.T().Skip("JFrog CLI not available")
		return
	}

	cmd := exec.Command("jf", "--version")
	output, err := cmd.Output()

	suite.NoError(err, "JFrog CLI should be executable")
	suite.Contains(string(output), "jfrog", "Output should contain 'jfrog'")

	suite.T().Logf("JFrog CLI Version: %s", string(output))
}

func (suite *IntegrationTestSuite) TestClientCreation() {
	if !suite.jfrogAvailable {
		suite.T().Skip("JFrog CLI not available")
		return
	}

	// Test with valid configuration
	client, err := NewClient(suite.config)

	if err != nil {
		// Expected if JFrog server is not accessible
		suite.Contains(err.Error(), "jfrog", "Error should mention JFrog")
		suite.T().Logf("Client creation failed as expected: %v", err)
		return
	}

	suite.NotNil(client, "Client should be created successfully")
	suite.Equal(suite.config, client.config, "Client should store config")
}

func (suite *IntegrationTestSuite) TestHealthCheck() {
	if suite.client == nil {
		suite.T().Skip("JFrog client not available")
		return
	}

	err := suite.client.Health(suite.ctx)

	if err != nil {
		// Expected if JFrog server is not accessible
		suite.Contains(err.Error(), "health", "Error should mention health check")
		suite.T().Logf("Health check failed as expected: %v", err)
	} else {
		suite.T().Log("Health check passed - JFrog server is accessible")
	}
}

func (suite *IntegrationTestSuite) TestValidateConnection() {
	if suite.client == nil {
		suite.T().Skip("JFrog client not available")
		return
	}

	err := suite.client.ValidateConnection(suite.ctx)

	if err != nil {
		// Expected if JFrog server is not accessible
		suite.Contains(err.Error(), "connection", "Error should mention connection")
		suite.T().Logf("Connection validation failed as expected: %v", err)
	} else {
		suite.T().Log("Connection validation passed - all JFrog services accessible")
	}
}

func (suite *IntegrationTestSuite) TestGetVersion() {
	if suite.client == nil {
		suite.T().Skip("JFrog client not available")
		return
	}

	versionInfo, err := suite.client.GetVersion(suite.ctx)

	if err != nil {
		suite.Contains(err.Error(), "version", "Error should mention version")
		suite.T().Logf("Get version failed: %v", err)
		return
	}

	suite.NotNil(versionInfo, "Version info should not be nil")
	suite.Contains(versionInfo, "jfrog_cli_version", "Should contain CLI version")
	suite.Contains(versionInfo, "client_version", "Should contain client version")

	suite.T().Logf("Version info: %+v", versionInfo)
}

func (suite *IntegrationTestSuite) TestUploadDownloadWorkflow() {
	if suite.client == nil {
		suite.T().Skip("JFrog client not available")
		return
	}

	// Create a test file
	testFile := filepath.Join(suite.tempDir, "test-artifact.txt")
	testContent := "This is a test artifact for JFrog integration testing"

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	suite.Require().NoError(err)

	// Test upload
	target := "test-repo/integration-test/test-artifact.txt"
	options := map[string]interface{}{
		"build_name":   "integration-test",
		"build_number": "1.0.0",
	}

	err = suite.client.Upload(suite.ctx, testFile, target, options)
	if err != nil {
		// Expected if repository doesn't exist or no permissions
		suite.Contains(err.Error(), "upload", "Error should mention upload")
		suite.T().Logf("Upload failed as expected: %v", err)
		return
	}

	suite.T().Log("Upload succeeded")

	// Test download
	downloadDir := filepath.Join(suite.tempDir, "downloads")
	err = os.MkdirAll(downloadDir, 0755)
	suite.Require().NoError(err)

	err = suite.client.Download(suite.ctx, target, downloadDir, options)
	if err != nil {
		suite.Contains(err.Error(), "download", "Error should mention download")
		suite.T().Logf("Download failed: %v", err)
		return
	}

	suite.T().Log("Download succeeded")

	// Verify downloaded file
	downloadedFile := filepath.Join(downloadDir, "test-artifact.txt")
	if _, err := os.Stat(downloadedFile); err == nil {
		content, err := os.ReadFile(downloadedFile)
		suite.NoError(err)
		suite.Equal(testContent, string(content), "Downloaded content should match uploaded content")
	}
}

func (suite *IntegrationTestSuite) TestSearchArtifacts() {
	if suite.client == nil {
		suite.T().Skip("JFrog client not available")
		return
	}

	pattern := "test-repo/*"

	artifacts, err := suite.client.Search(suite.ctx, pattern)
	if err != nil {
		// Expected if repository doesn't exist or no artifacts
		suite.Contains(err.Error(), "search", "Error should mention search")
		suite.T().Logf("Search failed as expected: %v", err)
		return
	}

	suite.T().Logf("Search found %d artifacts", len(artifacts))

	// Verify artifact structure if any found
	for _, artifact := range artifacts {
		suite.NotEmpty(artifact.URI, "Artifact should have URI")
		suite.NotEmpty(artifact.Repo, "Artifact should have repository")
	}
}

func (suite *IntegrationTestSuite) TestBuildOperations() {
	if suite.client == nil {
		suite.T().Skip("JFrog client not available")
		return
	}

	buildName := "integration-test-build"
	buildNumber := "1.0.0"

	// Test build environment collection
	err := suite.client.BuildCollectEnv(suite.ctx, buildName, buildNumber)
	if err != nil {
		suite.Contains(err.Error(), "build", "Error should mention build")
		suite.T().Logf("Build collect env failed: %v", err)
	} else {
		suite.T().Log("Build collect env succeeded")
	}

	// Test build publish
	err = suite.client.BuildPublish(suite.ctx, buildName, buildNumber)
	if err != nil {
		suite.Contains(err.Error(), "build", "Error should mention build")
		suite.T().Logf("Build publish failed: %v", err)
	} else {
		suite.T().Log("Build publish succeeded")
	}

	// Test get build info (returns simulated data)
	buildInfo, err := suite.client.GetBuildInfo(suite.ctx, buildName, buildNumber)
	suite.NoError(err, "GetBuildInfo should not fail (returns simulated data)")
	suite.NotNil(buildInfo, "Build info should not be nil")
	suite.Equal(buildName, buildInfo.Name, "Build name should match")
	suite.Equal(buildNumber, buildInfo.Number, "Build number should match")
}

func (suite *IntegrationTestSuite) TestSecurityScanning() {
	if suite.client == nil {
		suite.T().Skip("JFrog client not available")
		return
	}

	target := "test-repo"

	scanResult, err := suite.client.Scan(suite.ctx, target)

	// Scan returns simulated data, so should not fail
	suite.NoError(err, "Scan should not fail (returns simulated data)")
	suite.NotNil(scanResult, "Scan result should not be nil")
	suite.GreaterOrEqual(scanResult.Summary.TotalAlerts, 0, "Should have valid alert count")

	suite.T().Logf("Scan completed with %d alerts", scanResult.Summary.TotalAlerts)
}

func (suite *IntegrationTestSuite) TestCleanup() {
	if suite.client == nil {
		suite.T().Skip("JFrog client not available")
		return
	}

	err := suite.client.Cleanup(suite.ctx)
	suite.NoError(err, "Cleanup should not fail")

	suite.T().Log("Cleanup completed successfully")
}

// Helper function to get environment variable with default
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// TestIntegrationSuite runs the integration test suite
func TestIntegrationSuite(t *testing.T) {
	// Only run integration tests when explicitly requested
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	suite.Run(t, new(IntegrationTestSuite))
}

// TestJFrogCLIAvailability tests if JFrog CLI is available
func TestJFrogCLIAvailability(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping CLI availability test in short mode")
	}

	_, err := exec.LookPath("jf")
	if err != nil {
		t.Logf("JFrog CLI not found in PATH: %v", err)
		t.Log("To install JFrog CLI, visit: https://jfrog.com/getcli/")
		return
	}

	// Test CLI execution
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "jf", "--version")
	output, err := cmd.Output()

	require.NoError(t, err, "JFrog CLI should be executable")
	assert.Contains(t, string(output), "jfrog", "Output should contain 'jfrog'")

	t.Logf("JFrog CLI is available: %s", string(output))
}
