# MC Poly Installer - Universal Airgapped Pipeline CLI/TUI

A comprehensive command-line and terminal user interface tool for managing software delivery pipelines across airgapped environments. Integrates GitLab CI/CD, <PERSON><PERSON>, <PERSON> (CNAB), and JFrog CLI for secure, enterprise-grade deployment workflows.

## Architecture Overview

```
│ Artifactory Online │                        │ Artifactory OSS Offline │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Online GitLab │    │  Transmission   │    │   Airgapped     │
│                 │    │    Station      │    │  Environment    │
│  • Dagger      │◄──►│                 │◄──►│                 │
│  • Porter OCI   │    │  • Validation   │    │  • Dagger       │
│  • JFrog CLI    │    │  • Security     │    │  • Porter OCI   │
│  • CI/CD        │    │  • Transfer     │    │  • JFrog CLI    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Features

### 🎯 Core Capabilities
- **Multi-Environment Pipeline Orchestration**: Seamless workflow across online, transmission, and airgapped zones
- **Universal CLI Interface**: Single tool for all pipeline operations
- **Interactive TUI**: Beautiful terminal interface for complex operations
- **Security-First Design**: Zero-trust architecture with end-to-end verification
- **Compliance Automation**: Built-in SLSA 4+, SBOM, and audit trail generation

### 🛠️ Integrated Tools
- **Dagger**: CI/CD as code with Go SDK integration
- **Porter**: CNAB bundle management and OCI distribution
- **JFrog CLI**: Artifact management and distribution
- **GitLab**: Source control and CI/CD orchestration

### 🔒 Security Features
- End-to-end artifact verification
- Cryptographic signatures and attestations
- Supply chain security (SLSA 4+)
- Post-quantum cryptography ready
- Zero-trust transmission protocols

## Quick Start

```bash
# Install mc-poly
curl -fsSL https://install.mc-poly.dev | bash

# Initialize configuration
mc-poly init

# Launch TUI
mc-poly tui

# Or use CLI commands
mc-poly pipeline deploy --config production.yaml
```

## Command Structure

```
mc-poly
├── init              # Initialize configuration
├── config            # Configuration management
├── pipeline          # Pipeline operations
│   ├── create       # Create new pipeline
│   ├── deploy       # Deploy pipeline
│   ├── validate     # Validate configuration
│   └── status       # Check status
├── bundle            # Porter CNAB operations
│   ├── create       # Create bundle
│   ├── publish      # Publish to registry
│   ├── transfer     # Transfer through station
│   └── install      # Install in airgapped env
├── artifact          # JFrog artifact operations
│   ├── upload       # Upload artifacts
│   ├── download     # Download artifacts
│   ├── scan         # Security scanning
│   └── distribute   # Distribute to targets
├── transmission      # Transmission station operations
│   ├── setup        # Setup station
│   ├── validate     # Validate transfers
│   ├── monitor      # Monitor operations
│   └── audit        # Audit logs
└── tui               # Launch interactive interface
```

## Configuration

The tool uses a hierarchical configuration system:

```yaml
# ~/.mc-poly/config.yaml
environments:
  online:
    gitlab:
      url: "https://gitlab.company.com"
      token: "${GITLAB_TOKEN}"
    jfrog:
      url: "https://company.jfrog.io"
      username: "${JFROG_USER}"
      password: "${JFROG_PASSWORD}"
  
  transmission:
    endpoint: "https://transmission.secure.zone"
    auth:
      method: "mutual-tls"
      cert_path: "/path/to/client.crt"
      key_path: "/path/to/client.key"
  
  airgapped:
    jfrog:
      url: "https://internal.jfrog.local"
      username: "${AIRGAP_JFROG_USER}"
      password: "${AIRGAP_JFROG_PASSWORD}"

security:
  signing:
    enabled: true
    method: "sigstore"
  verification:
    required: true
    policies: ["slsa4", "sbom"]
  
dagger:
  engine_version: "0.13.0"
  cache_policy: "aggressive"

porter:
  registry: "company.jfrog.io/docker"
  default_driver: "docker"

compliance:
  audit_retention: "7y"
  sbom_format: ["spdx", "cyclonedx"]
  attestation_required: true
```

## License

MIT License - see [LICENSE](LICENSE) for details.

## Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for contribution guidelines.
